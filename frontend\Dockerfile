# Use Flutter official image
FROM cirrusci/flutter:3.24.3

# Set working directory
WORKDIR /app

# Set environment variables
ENV FLUTTER_ROOT=/usr/local/flutter
ENV PATH="$FLUTTER_ROOT/bin:$PATH"

# Install system dependencies
USER root
RUN apt-get update && apt-get install -y \
    curl \
    git \
    unzip \
    xz-utils \
    zip \
    libglu1-mesa \
    && rm -rf /var/lib/apt/lists/*

# Switch back to flutter user
USER flutter

# Enable web support
RUN flutter config --enable-web

# Copy pubspec files
COPY pubspec.yaml pubspec.lock ./

# Get dependencies
RUN flutter pub get

# Copy source code
COPY . .

# Build for web (development)
RUN flutter build web --debug

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# Start the application
CMD ["flutter", "run", "--web-port=8080", "--web-hostname=0.0.0.0", "--debug"]
