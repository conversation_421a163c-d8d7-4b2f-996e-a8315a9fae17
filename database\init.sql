-- BAC Study App Database Schema
-- This script initializes the database with all necessary tables

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    firebase_uid VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    wilaya VARCHAR(100), -- Algerian province
    bac_year INTEGER,
    bac_stream VARCHAR(50), -- Sciences, Lettres, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Subjects table
CREATE TABLE subjects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100), -- Arabic name
    name_fr VARCHAR(100), -- French name
    bac_stream VARCHAR(50),
    coefficient INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Content types
CREATE TYPE content_type AS ENUM ('pdf', 'video', 'image', 'text');
CREATE TYPE content_category AS ENUM ('lesson', 'exercise', 'exam', 'summary', 'book');

-- Content table
CREATE TABLE content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    title_ar VARCHAR(255),
    title_fr VARCHAR(255),
    description TEXT,
    content_type content_type NOT NULL,
    category content_category NOT NULL,
    subject_id INTEGER REFERENCES subjects(id),
    file_path VARCHAR(500),
    file_url VARCHAR(500),
    file_size BIGINT,
    duration INTEGER, -- For videos in seconds
    difficulty_level INTEGER DEFAULT 1, -- 1-5 scale
    year INTEGER, -- For past exams
    author VARCHAR(255),
    is_free BOOLEAN DEFAULT true,
    download_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Quizzes table
CREATE TABLE quizzes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    subject_id INTEGER REFERENCES subjects(id),
    difficulty_level INTEGER DEFAULT 1,
    time_limit INTEGER, -- in minutes
    total_questions INTEGER DEFAULT 0,
    is_assessment BOOLEAN DEFAULT false, -- true for initial assessment
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Quiz questions
CREATE TABLE quiz_questions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    quiz_id UUID REFERENCES quizzes(id) ON DELETE CASCADE,
    question_text TEXT NOT NULL,
    question_text_ar TEXT,
    question_text_fr TEXT,
    question_type VARCHAR(50) DEFAULT 'multiple_choice', -- multiple_choice, true_false, fill_blank
    correct_answer TEXT NOT NULL,
    explanation TEXT,
    points INTEGER DEFAULT 1,
    order_index INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Quiz answer options (for multiple choice)
CREATE TABLE quiz_answer_options (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    question_id UUID REFERENCES quiz_questions(id) ON DELETE CASCADE,
    option_text TEXT NOT NULL,
    option_text_ar TEXT,
    option_text_fr TEXT,
    is_correct BOOLEAN DEFAULT false,
    order_index INTEGER
);

-- User quiz attempts
CREATE TABLE user_quiz_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    quiz_id UUID REFERENCES quizzes(id),
    score DECIMAL(5,2),
    total_points INTEGER,
    time_taken INTEGER, -- in seconds
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    answers JSONB -- Store user answers
);

-- User progress tracking
CREATE TABLE user_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    subject_id INTEGER REFERENCES subjects(id),
    content_id UUID REFERENCES content(id),
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed BOOLEAN DEFAULT false,
    UNIQUE(user_id, content_id)
);

-- Discussion forums
CREATE TABLE forum_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100),
    name_fr VARCHAR(100),
    description TEXT,
    subject_id INTEGER REFERENCES subjects(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE forum_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id INTEGER REFERENCES forum_categories(id),
    user_id UUID REFERENCES users(id),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    is_pinned BOOLEAN DEFAULT false,
    is_locked BOOLEAN DEFAULT false,
    views_count INTEGER DEFAULT 0,
    replies_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE forum_replies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID REFERENCES forum_posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    content TEXT NOT NULL,
    parent_reply_id UUID REFERENCES forum_replies(id), -- For nested replies
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User favorites/bookmarks
CREATE TABLE user_favorites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    content_id UUID REFERENCES content(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, content_id)
);

-- Content ratings
CREATE TABLE content_ratings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    content_id UUID REFERENCES content(id),
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, content_id)
);

-- Study plans
CREATE TABLE study_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    target_exam_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE study_plan_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    study_plan_id UUID REFERENCES study_plans(id) ON DELETE CASCADE,
    content_id UUID REFERENCES content(id),
    scheduled_date DATE,
    completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP,
    order_index INTEGER
);

-- Insert default subjects for Algerian BAC
INSERT INTO subjects (name, name_ar, name_fr, bac_stream, coefficient) VALUES
('Mathematics', 'الرياضيات', 'Mathématiques', 'Sciences', 7),
('Physics', 'الفيزياء', 'Physique', 'Sciences', 6),
('Chemistry', 'الكيمياء', 'Chimie', 'Sciences', 4),
('Natural Sciences', 'علوم الطبيعة والحياة', 'Sciences Naturelles', 'Sciences', 6),
('Arabic Language', 'اللغة العربية وآدابها', 'Langue Arabe', 'All', 5),
('French Language', 'اللغة الفرنسية', 'Langue Française', 'All', 4),
('English Language', 'اللغة الإنجليزية', 'Langue Anglaise', 'All', 3),
('Philosophy', 'الفلسفة', 'Philosophie', 'Lettres', 7),
('History', 'التاريخ', 'Histoire', 'Lettres', 5),
('Geography', 'الجغرافيا', 'Géographie', 'Lettres', 4),
('Islamic Studies', 'العلوم الإسلامية', 'Sciences Islamiques', 'All', 2),
('Physical Education', 'التربية البدنية', 'Éducation Physique', 'All', 1);

-- Insert default forum categories
INSERT INTO forum_categories (name, name_ar, name_fr, subject_id) VALUES
('General Discussion', 'نقاش عام', 'Discussion Générale', NULL),
('Study Tips', 'نصائح للدراسة', 'Conseils d''Étude', NULL),
('Exam Preparation', 'التحضير للامتحان', 'Préparation aux Examens', NULL);

-- Create indexes for better performance
CREATE INDEX idx_users_firebase_uid ON users(firebase_uid);
CREATE INDEX idx_content_subject_id ON content(subject_id);
CREATE INDEX idx_content_category ON content(category);
CREATE INDEX idx_quiz_attempts_user_id ON user_quiz_attempts(user_id);
CREATE INDEX idx_user_progress_user_id ON user_progress(user_id);
CREATE INDEX idx_forum_posts_category_id ON forum_posts(category_id);
CREATE INDEX idx_forum_replies_post_id ON forum_replies(post_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_updated_at BEFORE UPDATE ON content FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_forum_posts_updated_at BEFORE UPDATE ON forum_posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_forum_replies_updated_at BEFORE UPDATE ON forum_replies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
