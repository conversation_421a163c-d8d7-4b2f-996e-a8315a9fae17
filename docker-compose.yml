version: '3.8'

services:
  # Flutter Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"  # Flutter web for development
    volumes:
      - ./frontend:/app
      - /app/build  # Prevent overwriting build files
    environment:
      - FLUTTER_ENV=development
      - API_BASE_URL=http://backend:3000
    depends_on:
      - backend

  # Node.js Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - ./content:/app/content  # Content storage
      - /app/node_modules  # Prevent overwriting node_modules
    depends_on:
      - db
    environment:
      - NODE_ENV=development
      - DATABASE_URL=****************************************/bac_db
      - JWT_SECRET=your_jwt_secret_here
      - FIREBASE_PROJECT_ID=your_firebase_project_id

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=bac_user
      - POSTGRES_PASSWORD=bac_password
      - POSTGRES_DB=bac_db
    volumes:
      - pgdata:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redisdata:/data

  # Nginx for production (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./content:/usr/share/nginx/html/content
    depends_on:
      - backend
    profiles:
      - production

volumes:
  pgdata:
  redisdata:

networks:
  default:
    driver: bridge
