const express = require('express');
const path = require('path');

const app = express();
const PORT = 3000;

// Middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});
app.use(express.json());
app.use(express.static(path.join(__dirname, 'frontend', 'web')));

// Test endpoints for our new structure
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'BAC Study App Backend is running with new structure!',
    timestamp: new Date().toISOString(),
    structure: {
      frontend: 'Flutter with comprehensive architecture',
      backend: 'Node.js with modular structure',
      database: 'PostgreSQL with complete schema',
      features: [
        'Authentication system',
        'User management',
        'Content management',
        'Quiz system',
        'Forum system',
        'Progress tracking'
      ]
    }
  });
});

app.get('/api/structure', (req, res) => {
  res.json({
    message: 'Project structure implemented successfully!',
    frontend: {
      core: {
        config: ['app_config.dart', 'theme_config.dart', 'api_config.dart'],
        router: ['app_router.dart', 'route_guards.dart'],
        services: ['api_service.dart', 'auth_service.dart', 'storage_service.dart', 'notification_service.dart'],
        utils: ['validators.dart', 'formatters.dart', 'extensions.dart']
      },
      features: {
        auth: ['screens/', 'widgets/', 'models/', 'providers/'],
        quiz: ['screens/', 'widgets/', 'models/', 'providers/'],
        content: ['screens/', 'widgets/', 'models/', 'providers/'],
        forum: ['screens/', 'widgets/', 'models/', 'providers/']
      },
      shared: ['widgets/', 'constants/', 'styles/']
    },
    backend: {
      controllers: ['auth.controller.js', 'user.controller.js', 'subject.controller.js', 'content.controller.js', 'quiz.controller.js', 'forum.controller.js', 'progress.controller.js'],
      services: ['auth.service.js', 'user.service.js', 'email.service.js', 'file.service.js', 'quiz.service.js', 'cache.service.js'],
      middleware: ['auth.middleware.js', 'error.middleware.js', 'validation.middleware.js', 'upload.middleware.js'],
      models: ['user.model.js', 'subject.model.js', 'content.model.js', 'quiz.model.js', 'forum.model.js', 'progress.model.js'],
      routes: ['auth.routes.js', 'user.routes.js', 'subject.routes.js', 'content.routes.js', 'quiz.routes.js', 'forum.routes.js', 'progress.routes.js'],
      utils: ['logger.js', 'helpers.js', 'validators.js'],
      database: ['connection.js', 'queries.js', 'redis.js']
    },
    status: 'Structure created and ready for development!'
  });
});

// Test authentication endpoint
app.post('/api/auth/test', (req, res) => {
  res.json({
    message: 'Authentication controller structure ready!',
    endpoints: [
      'POST /api/auth/register',
      'POST /api/auth/login',
      'POST /api/auth/refresh',
      'POST /api/auth/logout',
      'GET /api/auth/profile',
      'PUT /api/auth/profile',
      'POST /api/auth/change-password',
      'POST /api/auth/request-password-reset',
      'GET /api/auth/verify-email/:token',
      'POST /api/auth/resend-verification'
    ],
    features: [
      'Firebase Authentication integration',
      'JWT token management',
      'User registration and login',
      'Password reset functionality',
      'Email verification',
      'Profile management'
    ]
  });
});

// Test Flutter features endpoint
app.get('/api/flutter/features', (req, res) => {
  res.json({
    message: 'Flutter architecture implemented!',
    features: {
      core_services: [
        'API Service with Dio HTTP client',
        'Authentication Service with Firebase',
        'Storage Service for local data',
        'Notification Service for push notifications'
      ],
      utilities: [
        'Form validators for Arabic/French/English',
        'Date/time formatters with Arabic support',
        'String extensions for Arabic text',
        'Responsive design helpers'
      ],
      routing: [
        'GoRouter configuration',
        'Route guards for authentication',
        'Role-based access control',
        'Deep linking support'
      ],
      state_management: [
        'Riverpod providers',
        'Authentication state',
        'User profile state',
        'App settings state'
      ]
    },
    ready_for: [
      'User authentication screens',
      'Quiz taking interface',
      'Content viewing (PDF, video)',
      'Forum discussions',
      'Progress tracking dashboard'
    ]
  });
});

// Serve Flutter web app
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend', 'web', 'index.html'));
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🎓 BAC Study App - Complete Structure Test Server`);
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📱 Frontend: http://localhost:${PORT}`);
  console.log(`🔧 Backend API: http://localhost:${PORT}/api`);
  console.log(`📊 Structure Info: http://localhost:${PORT}/api/structure`);
  console.log(`🔐 Auth Test: http://localhost:${PORT}/api/auth/test`);
  console.log(`📱 Flutter Features: http://localhost:${PORT}/api/flutter/features`);
  console.log(`\n✅ Complete project structure implemented!`);
  console.log(`\n📋 Next Steps:`);
  console.log(`   1. Test the structure endpoints above`);
  console.log(`   2. Start implementing individual features`);
  console.log(`   3. Create UI screens for authentication`);
  console.log(`   4. Build quiz taking interface`);
  console.log(`   5. Add content management features`);
});

module.exports = app;
