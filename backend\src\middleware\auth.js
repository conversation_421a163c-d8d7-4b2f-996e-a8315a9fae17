const jwt = require('jsonwebtoken');
const admin = require('firebase-admin');
const { query } = require('../database/connection');
const { AppError, catchAsync } = require('./errorHandler');
const config = require('../config/config');
const logger = require('../utils/logger');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: config.firebase.projectId,
      privateKey: config.firebase.privateKey,
      clientEmail: config.firebase.clientEmail,
    }),
    databaseURL: config.firebase.databaseURL,
  });
}

// Verify JWT token
const verifyJWT = (token) => {
  return new Promise((resolve, reject) => {
    jwt.verify(token, config.jwt.secret, (err, decoded) => {
      if (err) {
        reject(err);
      } else {
        resolve(decoded);
      }
    });
  });
};

// Verify Firebase token
const verifyFirebaseToken = async (token) => {
  try {
    const decodedToken = await admin.auth().verifyIdToken(token);
    return decodedToken;
  } catch (error) {
    throw new AppError('Invalid Firebase token', 401);
  }
};

// Get user from database
const getUserFromDB = async (firebaseUid) => {
  const result = await query(
    'SELECT * FROM users WHERE firebase_uid = $1',
    [firebaseUid]
  );
  
  if (result.rows.length === 0) {
    throw new AppError('User not found', 404);
  }
  
  return result.rows[0];
};

// Main authentication middleware
const authMiddleware = catchAsync(async (req, res, next) => {
  // 1) Getting token and check if it's there
  let token;
  
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (!token) {
    return next(new AppError('You are not logged in! Please log in to get access.', 401));
  }

  try {
    // 2) Try to verify JWT token first (for API calls)
    const decoded = await verifyJWT(token);
    
    // 3) Check if user still exists
    const currentUser = await getUserFromDB(decoded.firebaseUid);
    
    if (!currentUser) {
      return next(new AppError('The user belonging to this token does no longer exist.', 401));
    }

    // 4) Grant access to protected route
    req.user = currentUser;
    next();
    
  } catch (jwtError) {
    // If JWT verification fails, try Firebase token
    try {
      const firebaseDecoded = await verifyFirebaseToken(token);
      
      // Get user from database
      const currentUser = await getUserFromDB(firebaseDecoded.uid);
      
      if (!currentUser) {
        return next(new AppError('The user belonging to this token does no longer exist.', 401));
      }

      // Grant access to protected route
      req.user = currentUser;
      next();
      
    } catch (firebaseError) {
      logger.error('Authentication failed:', { jwtError, firebaseError });
      return next(new AppError('Invalid token. Please log in again!', 401));
    }
  }
});

// Optional authentication middleware (doesn't throw error if no token)
const optionalAuth = catchAsync(async (req, res, next) => {
  let token;
  
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (!token) {
    return next();
  }

  try {
    const decoded = await verifyJWT(token);
    const currentUser = await getUserFromDB(decoded.firebaseUid);
    
    if (currentUser) {
      req.user = currentUser;
    }
  } catch (error) {
    // Silently fail for optional auth
    logger.debug('Optional auth failed:', error.message);
  }
  
  next();
});

// Role-based authorization middleware
const restrictTo = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('You are not logged in!', 401));
    }
    
    if (!roles.includes(req.user.role)) {
      return next(new AppError('You do not have permission to perform this action', 403));
    }
    
    next();
  };
};

// Check if user owns the resource
const checkOwnership = (resourceField = 'user_id') => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('You are not logged in!', 401));
    }
    
    // Admin can access everything
    if (req.user.role === 'admin') {
      return next();
    }
    
    // Check if user owns the resource
    const resourceUserId = req.params.userId || req.body[resourceField] || req.query[resourceField];
    
    if (resourceUserId && resourceUserId !== req.user.id) {
      return next(new AppError('You can only access your own resources', 403));
    }
    
    next();
  };
};

// Rate limiting per user
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();
  
  return (req, res, next) => {
    if (!req.user) {
      return next();
    }
    
    const userId = req.user.id;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get user's requests
    let userRequests = requests.get(userId) || [];
    
    // Filter out old requests
    userRequests = userRequests.filter(timestamp => timestamp > windowStart);
    
    // Check if limit exceeded
    if (userRequests.length >= maxRequests) {
      return next(new AppError('Too many requests. Please try again later.', 429));
    }
    
    // Add current request
    userRequests.push(now);
    requests.set(userId, userRequests);
    
    next();
  };
};

// Generate JWT token
const generateJWT = (payload) => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  });
};

// Generate refresh token
const generateRefreshToken = (payload) => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.refreshExpiresIn,
  });
};

module.exports = {
  authMiddleware,
  optionalAuth,
  restrictTo,
  checkOwnership,
  userRateLimit,
  generateJWT,
  generateRefreshToken,
  verifyFirebaseToken,
  getUserFromDB
};
