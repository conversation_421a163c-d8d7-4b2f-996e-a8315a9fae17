const request = require('supertest');
const { app } = require('../src/server');
const { query } = require('../src/database/connection');

describe('Authentication Endpoints', () => {
  // Mock Firebase token for testing
  const mockFirebaseToken = 'mock-firebase-token';
  const mockUserData = {
    firebase_token: mockFirebaseToken,
    full_name: '<PERSON>',
    wilaya: 'الجزائر',
    bac_year: 2024,
    bac_stream: 'علوم تجريبية'
  };

  beforeAll(async () => {
    // Setup test database or mock
    // This would typically involve setting up a test database
  });

  afterAll(async () => {
    // Cleanup test data
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user with valid data', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send(mockUserData)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data.user).toHaveProperty('id');
      expect(response.body.data.user.full_name).toBe(mockUserData.full_name);
      expect(response.body.data.tokens).toHaveProperty('access_token');
      expect(response.body.data.tokens).toHaveProperty('refresh_token');
    });

    it('should return 400 for invalid data', async () => {
      const invalidData = {
        firebase_token: mockFirebaseToken,
        full_name: 'A', // Too short
        wilaya: '',
        bac_year: 2019, // Too old
        bac_stream: 'Invalid Stream'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.status).toBe('fail');
    });

    it('should return 409 for duplicate user', async () => {
      // First registration
      await request(app)
        .post('/api/auth/register')
        .send(mockUserData);

      // Second registration with same data
      const response = await request(app)
        .post('/api/auth/register')
        .send(mockUserData)
        .expect(409);

      expect(response.body.status).toBe('fail');
    });
  });

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // Register a user for login tests
      await request(app)
        .post('/api/auth/register')
        .send(mockUserData);
    });

    it('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({ firebase_token: mockFirebaseToken })
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.user).toHaveProperty('id');
      expect(response.body.data.tokens).toHaveProperty('access_token');
    });

    it('should return 401 for invalid token', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({ firebase_token: 'invalid-token' })
        .expect(401);

      expect(response.body.status).toBe('fail');
    });

    it('should return 404 for non-existent user', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({ firebase_token: 'non-existent-user-token' })
        .expect(404);

      expect(response.body.status).toBe('fail');
    });
  });

  describe('POST /api/auth/refresh', () => {
    let refreshToken;

    beforeEach(async () => {
      // Register and login to get refresh token
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(mockUserData);
      
      refreshToken = registerResponse.body.data.tokens.refresh_token;
    });

    it('should refresh token with valid refresh token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refresh_token: refreshToken })
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toHaveProperty('access_token');
    });

    it('should return 401 for invalid refresh token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refresh_token: 'invalid-refresh-token' })
        .expect(401);

      expect(response.body.status).toBe('fail');
    });
  });
});
