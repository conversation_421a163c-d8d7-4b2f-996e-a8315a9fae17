import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/config/app_config.dart';

class SettingsState {
  final ThemeMode themeMode;
  final Locale locale;
  final double textScaleFactor;

  const SettingsState({
    this.themeMode = AppConfig.defaultThemeMode,
    this.locale = AppConfig.defaultLocale,
    this.textScaleFactor = AppConfig.defaultTextScaleFactor,
  });

  SettingsState copyWith({
    ThemeMode? themeMode,
    Locale? locale,
    double? textScaleFactor,
  }) {
    return SettingsState(
      themeMode: themeMode ?? this.themeMode,
      locale: locale ?? this.locale,
      textScaleFactor: textScaleFactor ?? this.textScaleFactor,
    );
  }
}

class SettingsNotifier extends StateNotifier<SettingsState> {
  SettingsNotifier() : super(const SettingsState());

  void setThemeMode(ThemeMode themeMode) {
    state = state.copyWith(themeMode: themeMode);
  }

  void setLocale(Locale locale) {
    state = state.copyWith(locale: locale);
  }

  void setTextScaleFactor(double factor) {
    state = state.copyWith(textScaleFactor: factor);
  }
}

final settingsProvider = StateNotifierProvider<SettingsNotifier, SettingsState>((ref) {
  return SettingsNotifier();
});
