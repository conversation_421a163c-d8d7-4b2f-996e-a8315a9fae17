# 🎓 BAC Study App - تطبيق دراسة البكالوريا

A comprehensive educational mobile application designed specifically for Algerian BAC (Baccalauréat) students. This app provides personalized learning experiences, study materials, quizzes, and community features to help students excel in their final exams.

## 🌟 Features

### 📚 Core Educational Features
- **Personalized Assessment**: Initial quiz to identify strengths and weaknesses
- **Comprehensive Content Library**: PDFs, videos, images, and interactive lessons
- **Past BAC Exams**: Complete collection with detailed corrections
- **Subject-Specific Materials**: Organized by Algerian BAC streams (Sciences, Lettres, etc.)
- **Offline Support**: Download content for studying without internet

### 🎯 Learning & Progress Tracking
- **Adaptive Learning**: Personalized recommendations based on performance
- **Progress Analytics**: Detailed insights into study patterns and improvements
- **Study Plans**: Customizable schedules leading up to exam dates
- **Performance Metrics**: Track scores, time spent, and completion rates

### 👥 Community Features
- **Discussion Forums**: Subject-specific discussion boards
- **Peer Learning**: Connect with fellow students across Algeria
- **Teacher Resources**: Access to lessons from experienced educators
- **Study Groups**: Collaborative learning environments

### 📱 Modern Mobile Experience
- **Cross-Platform**: Available for both Android and iOS
- **Multilingual Support**: Arabic, French, and English
- **Dark/Light Themes**: Comfortable studying in any lighting
- **Responsive Design**: Optimized for all screen sizes

## 🛠 Technology Stack

### Frontend (Mobile App)
- **Framework**: Flutter 3.24.3
- **State Management**: Riverpod
- **Navigation**: GoRouter
- **Local Storage**: Hive + SQLite
- **UI Components**: Material Design 3

### Backend (API Server)
- **Runtime**: Node.js 20 LTS
- **Framework**: Express.js
- **Database**: PostgreSQL 15
- **Caching**: Redis
- **Authentication**: Firebase Auth + JWT

### DevOps & Infrastructure
- **Containerization**: Docker & Docker Compose
- **Development**: Hot reload with volume mounting
- **Testing**: Jest (Backend) + Flutter Test (Frontend)
- **Documentation**: Swagger/OpenAPI 3.0

### Security & Performance
- **Security**: Helmet.js, Rate limiting, Input validation
- **File Storage**: Local volumes (dev) + Cloudinary (production)
- **Real-time**: Socket.IO for live features
- **Monitoring**: Winston logging + Health checks

## 🚀 Quick Start

### Prerequisites
- [Docker Desktop](https://www.docker.com/products/docker-desktop/) (Required)
- [Git](https://git-scm.com/) (Required)
- [VS Code](https://code.visualstudio.com/) (Recommended)

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/BAC_App.git
cd BAC_App
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
# At minimum, set these required variables:
# - JWT_SECRET (generate a strong random string)
# - FIREBASE_PROJECT_ID (create a Firebase project)
```

### 3. Start the Application
```bash
# Start all services (database, backend, frontend)
docker-compose up --build

# Or run in background
docker-compose up -d --build
```

### 4. Access the Application
- **Frontend (Flutter Web)**: http://localhost:8080
- **Backend API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api-docs
- **Database**: localhost:5432 (use any PostgreSQL client)

### 5. Initial Setup
```bash
# Run database migrations (if needed)
docker-compose exec backend npm run migrate

# Seed initial data
docker-compose exec backend npm run seed
```

## 📱 Development Workflow

### Frontend Development (Flutter)
```bash
# Access Flutter container
docker-compose exec frontend bash

# Run tests
flutter test

# Build for Android
flutter build apk

# Build for iOS (requires macOS)
flutter build ios
```

### Backend Development (Node.js)
```bash
# Access backend container
docker-compose exec backend bash

# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Lint code
npm run lint
```

### Database Operations
```bash
# Access PostgreSQL
docker-compose exec db psql -U bac_user -d bac_db

# View logs
docker-compose logs db

# Backup database
docker-compose exec db pg_dump -U bac_user bac_db > backup.sql
```

## 🧪 Testing

### Automated Testing
```bash
# Run all tests
docker-compose exec backend npm test
docker-compose exec frontend flutter test

# Run integration tests
docker-compose exec frontend flutter test integration_test/

# Run API tests
docker-compose exec backend npm run test:api
```

### Manual Testing
1. **Authentication Flow**: Sign up → Login → Profile setup
2. **Quiz System**: Take assessment → View results → Get recommendations
3. **Content Access**: Browse subjects → View PDFs → Watch videos
4. **Forum Features**: Create post → Reply to discussions
5. **Offline Mode**: Download content → Disconnect internet → Verify access

## 📊 Project Structure

```
BAC_App/
├── frontend/                 # Flutter mobile application
│   ├── lib/
│   │   ├── core/            # Core utilities, config, services
│   │   ├── features/        # Feature-based modules
│   │   ├── shared/          # Shared widgets and utilities
│   │   └── main.dart        # Application entry point
│   ├── test/                # Unit and widget tests
│   ├── integration_test/    # Integration tests
│   └── pubspec.yaml         # Flutter dependencies
│
├── backend/                 # Node.js API server
│   ├── src/
│   │   ├── config/          # Configuration files
│   │   ├── controllers/     # Request handlers
│   │   ├── middleware/      # Express middleware
│   │   ├── models/          # Data models
│   │   ├── routes/          # API routes
│   │   ├── services/        # Business logic
│   │   └── utils/           # Utility functions
│   ├── test/                # Backend tests
│   └── package.json         # Node.js dependencies
│
├── database/                # Database configuration
│   ├── init.sql            # Initial schema and data
│   └── migrations/         # Database migrations
│
├── content/                 # Static content storage
│   ├── uploads/            # User uploaded files
│   └── static/             # Static assets
│
├── docker-compose.yml       # Container orchestration
├── .env.example            # Environment template
└── README.md               # This file
```

## 🔧 Configuration

### Firebase Setup
1. Create a new Firebase project at https://console.firebase.google.com
2. Enable Authentication with Email/Password and Google Sign-In
3. Generate a service account key
4. Update `.env` file with Firebase credentials

### Database Configuration
The PostgreSQL database is automatically initialized with:
- User management tables
- Content and quiz structures
- Forum and discussion tables
- Progress tracking tables
- Algerian BAC-specific subjects

### Content Management
- **PDFs**: Store in `content/uploads/pdfs/`
- **Videos**: Use YouTube embedding or local storage
- **Images**: Optimized and cached automatically
- **Metadata**: Stored in PostgreSQL for fast searching

## 🚀 Deployment

### Development Deployment
```bash
# Start development environment
docker-compose up -d

# View logs
docker-compose logs -f
```

### Production Deployment
```bash
# Use production profile
docker-compose --profile production up -d

# Or deploy to cloud platforms:
# - Render.com (Backend)
# - Vercel (Frontend)
# - Supabase (Database)
```

## 📈 Roadmap

### Phase 1 (Current) - MVP
- [x] Basic authentication system
- [x] Quiz and assessment engine
- [x] Content management system
- [x] Discussion forums
- [ ] Mobile app builds

### Phase 2 - Enhanced Features
- [ ] Advanced analytics dashboard
- [ ] Video streaming optimization
- [ ] Offline synchronization
- [ ] Push notifications

### Phase 3 - Community & Gamification
- [ ] Achievement system
- [ ] Leaderboards
- [ ] Study streaks
- [ ] Peer tutoring features

### Phase 4 - AI & Personalization
- [ ] AI-powered recommendations
- [ ] Automated content tagging
- [ ] Intelligent study scheduling
- [ ] Performance prediction

## 🤝 Contributing

We welcome contributions from the Algerian developer community!

### How to Contribute
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow Flutter and Node.js best practices
- Write tests for new features
- Update documentation
- Use conventional commit messages
- Ensure Docker builds pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Getting Help
- **Documentation**: Check this README and inline code comments
- **Issues**: Report bugs on GitHub Issues
- **Discussions**: Use GitHub Discussions for questions
- **Email**: <EMAIL> (when available)

### Common Issues
1. **Docker not starting**: Ensure Docker Desktop is running
2. **Port conflicts**: Check if ports 3000, 5432, 8080 are available
3. **Firebase errors**: Verify Firebase configuration in `.env`
4. **Database connection**: Ensure PostgreSQL container is healthy

## 🙏 Acknowledgments

- **Algerian Ministry of Education**: For BAC curriculum guidelines
- **Open Source Community**: For the amazing tools and libraries
- **Beta Testers**: Algerian students who provided valuable feedback
- **Contributors**: Everyone who helped build this platform

---

**Made with ❤️ for Algerian students preparing for their BAC exams**

*"التعليم هو أقوى سلاح يمكن استخدامه لتغيير العالم" - نيلسون مانديلا*
