class Validators {
  // Email validation
  static String? email(String? value) {
    if (value == null || value.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }
    
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return 'البريد الإلكتروني غير صحيح';
    }
    
    return null;
  }

  // Password validation
  static String? password(String? value) {
    if (value == null || value.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }
    
    if (value.length < 8) {
      return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }
    
    if (!value.contains(RegExp(r'[A-Z]'))) {
      return 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
    }
    
    if (!value.contains(RegExp(r'[a-z]'))) {
      return 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
    }
    
    if (!value.contains(RegExp(r'[0-9]'))) {
      return 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
    }
    
    return null;
  }

  // Confirm password validation
  static String? confirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'تأكيد كلمة المرور مطلوب';
    }
    
    if (value != password) {
      return 'كلمة المرور غير متطابقة';
    }
    
    return null;
  }

  // Required field validation
  static String? required(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'هذا الحقل'} مطلوب';
    }
    return null;
  }

  // Name validation
  static String? name(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'الاسم مطلوب';
    }
    
    if (value.trim().length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }
    
    if (value.trim().length > 50) {
      return 'الاسم يجب أن يكون أقل من 50 حرف';
    }
    
    // Check for valid characters (Arabic, English, spaces)
    final nameRegex = RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$');
    if (!nameRegex.hasMatch(value.trim())) {
      return 'الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط';
    }
    
    return null;
  }

  // Phone number validation (Algerian format)
  static String? phoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'رقم الهاتف مطلوب';
    }
    
    // Remove spaces and special characters
    final cleanNumber = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // Algerian phone number patterns
    final algerianMobileRegex = RegExp(r'^(0|\+213)[567]\d{8}$');
    
    if (!algerianMobileRegex.hasMatch(cleanNumber)) {
      return 'رقم الهاتف غير صحيح (مثال: 0555123456)';
    }
    
    return null;
  }

  // BAC year validation
  static String? bacYear(String? value) {
    if (value == null || value.isEmpty) {
      return 'سنة البكالوريا مطلوبة';
    }
    
    final year = int.tryParse(value);
    if (year == null) {
      return 'سنة البكالوريا يجب أن تكون رقم';
    }
    
    final currentYear = DateTime.now().year;
    if (year < currentYear - 5 || year > currentYear + 2) {
      return 'سنة البكالوريا غير صحيحة';
    }
    
    return null;
  }

  // Age validation
  static String? age(String? value) {
    if (value == null || value.isEmpty) {
      return 'العمر مطلوب';
    }
    
    final ageValue = int.tryParse(value);
    if (ageValue == null) {
      return 'العمر يجب أن يكون رقم';
    }
    
    if (ageValue < 16 || ageValue > 25) {
      return 'العمر يجب أن يكون بين 16 و 25 سنة';
    }
    
    return null;
  }

  // URL validation
  static String? url(String? value) {
    if (value == null || value.isEmpty) {
      return null; // URL is optional
    }
    
    final urlRegex = RegExp(
      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$'
    );
    
    if (!urlRegex.hasMatch(value)) {
      return 'الرابط غير صحيح';
    }
    
    return null;
  }

  // Text length validation
  static String? textLength(String? value, int minLength, int maxLength, [String? fieldName]) {
    if (value == null || value.isEmpty) {
      return '${fieldName ?? 'هذا الحقل'} مطلوب';
    }
    
    if (value.length < minLength) {
      return '${fieldName ?? 'هذا الحقل'} يجب أن يكون $minLength أحرف على الأقل';
    }
    
    if (value.length > maxLength) {
      return '${fieldName ?? 'هذا الحقل'} يجب أن يكون أقل من $maxLength حرف';
    }
    
    return null;
  }

  // Number validation
  static String? number(String? value, {int? min, int? max, String? fieldName}) {
    if (value == null || value.isEmpty) {
      return '${fieldName ?? 'هذا الحقل'} مطلوب';
    }
    
    final number = int.tryParse(value);
    if (number == null) {
      return '${fieldName ?? 'هذا الحقل'} يجب أن يكون رقم';
    }
    
    if (min != null && number < min) {
      return '${fieldName ?? 'هذا الحقل'} يجب أن يكون $min على الأقل';
    }
    
    if (max != null && number > max) {
      return '${fieldName ?? 'هذا الحقل'} يجب أن يكون $max على الأكثر';
    }
    
    return null;
  }

  // Decimal validation
  static String? decimal(String? value, {double? min, double? max, String? fieldName}) {
    if (value == null || value.isEmpty) {
      return '${fieldName ?? 'هذا الحقل'} مطلوب';
    }
    
    final number = double.tryParse(value);
    if (number == null) {
      return '${fieldName ?? 'هذا الحقل'} يجب أن يكون رقم';
    }
    
    if (min != null && number < min) {
      return '${fieldName ?? 'هذا الحقل'} يجب أن يكون $min على الأقل';
    }
    
    if (max != null && number > max) {
      return '${fieldName ?? 'هذا الحقل'} يجب أن يكون $max على الأكثر';
    }
    
    return null;
  }

  // Custom validation
  static String? custom(String? value, bool Function(String) validator, String errorMessage) {
    if (value == null || value.isEmpty) {
      return null;
    }
    
    if (!validator(value)) {
      return errorMessage;
    }
    
    return null;
  }

  // Combine multiple validators
  static String? combine(String? value, List<String? Function(String?)> validators) {
    for (final validator in validators) {
      final result = validator(value);
      if (result != null) {
        return result;
      }
    }
    return null;
  }
}
