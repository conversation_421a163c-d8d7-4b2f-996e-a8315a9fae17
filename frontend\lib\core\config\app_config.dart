import 'package:flutter/material.dart';

class AppConfig {
  // App Information
  static const String appName = 'BAC Study App';
  static const String appNameAr = 'تطبيق دراسة البكالوريا';
  static const String appNameFr = 'Application d\'Étude BAC';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';

  // Environment
  static const bool isDevelopment = bool.fromEnvironment('dart.vm.product') == false;
  static const bool isProduction = bool.fromEnvironment('dart.vm.product');

  // API Configuration
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'http://localhost:3000',
  );
  static const String apiVersion = 'v1';
  static const String apiPrefix = '/api';

  // Firebase Configuration
  static const String firebaseProjectId = String.fromEnvironment(
    'FIREBASE_PROJECT_ID',
    defaultValue: 'bac-study-app-dev',
  );

  // Supported Languages
  static const List<Locale> supportedLocales = [
    Locale('ar', 'DZ'), // Arabic (Algeria)
    Locale('fr', 'DZ'), // French (Algeria)
    Locale('en', 'US'), // English (US)
  ];

  // Default Settings
  static const Locale defaultLocale = Locale('ar', 'DZ');
  static const ThemeMode defaultThemeMode = ThemeMode.light;
  static const double defaultTextScaleFactor = 1.0;

  // App Limits
  static const int maxFileUploadSize = 50 * 1024 * 1024; // 50MB
  static const int maxImageUploadSize = 10 * 1024 * 1024; // 10MB
  static const int maxVideoUploadSize = 100 * 1024 * 1024; // 100MB

  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Quiz Configuration
  static const int defaultQuizTimeLimit = 30; // minutes
  static const int maxQuizQuestions = 50;
  static const double passingScore = 60.0; // percentage

  // Content Configuration
  static const List<String> supportedImageFormats = [
    'jpg', 'jpeg', 'png', 'gif', 'webp'
  ];
  static const List<String> supportedDocumentFormats = [
    'pdf', 'doc', 'docx'
  ];
  static const List<String> supportedVideoFormats = [
    'mp4', 'avi', 'mov', 'wmv'
  ];

  // Algerian Wilayas
  static const List<String> algerianWilayas = [
    'أدرار', 'الشلف', 'الأغواط', 'أم البواقي', 'باتنة', 'بجاية', 'بسكرة',
    'بشار', 'البليدة', 'البويرة', 'تمنراست', 'تبسة', 'تلمسان', 'تيارت',
    'تيزي وزو', 'الجزائر', 'الجلفة', 'جيجل', 'سطيف', 'سعيدة', 'سكيكدة',
    'سيدي بلعباس', 'عنابة', 'قالمة', 'قسنطينة', 'المدية', 'مستغانم',
    'المسيلة', 'معسكر', 'ورقلة', 'وهران', 'البيض', 'إليزي', 'برج بوعريريج',
    'بومرداس', 'الطارف', 'تندوف', 'تيسمسيلت', 'الوادي', 'خنشلة',
    'سوق أهراس', 'تيبازة', 'ميلة', 'عين الدفلى', 'النعامة', 'عين تموشنت',
    'غرداية', 'غليزان', 'تيميمون', 'برج باجي مختار', 'أولاد جلال',
    'بني عباس', 'عين صالح', 'عين قزام', 'تقرت', 'جانت', 'المغير', 'المنيعة'
  ];

  // BAC Streams
  static const List<String> bacStreams = [
    'علوم تجريبية', // Sciences Expérimentales
    'رياضيات', // Mathématiques
    'تقني رياضي', // Technique Mathématique
    'تسيير واقتصاد', // Gestion et Économie
    'آداب وفلسفة', // Lettres et Philosophie
    'لغات أجنبية', // Langues Étrangères
  ];

  // App Colors (Material 3)
  static const Color primaryColor = Color(0xFF1976D2); // Blue
  static const Color secondaryColor = Color(0xFF388E3C); // Green
  static const Color errorColor = Color(0xFFD32F2F); // Red
  static const Color warningColor = Color(0xFFF57C00); // Orange
  static const Color successColor = Color(0xFF388E3C); // Green
  static const Color infoColor = Color(0xFF1976D2); // Blue

  // App URLs
  static const String privacyPolicyUrl = 'https://bacstudy.dz/privacy';
  static const String termsOfServiceUrl = 'https://bacstudy.dz/terms';
  static const String supportEmail = '<EMAIL>';
  static const String websiteUrl = 'https://bacstudy.dz';

  // Social Media
  static const String facebookUrl = 'https://facebook.com/bacstudyapp';
  static const String twitterUrl = 'https://twitter.com/bacstudyapp';
  static const String instagramUrl = 'https://instagram.com/bacstudyapp';
  static const String youtubeUrl = 'https://youtube.com/bacstudyapp';

  // Feature Flags
  static const bool enableOfflineMode = true;
  static const bool enablePushNotifications = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  static const bool enableForum = true;
  static const bool enableVideoStreaming = true;

  // Debug Settings
  static const bool showDebugInfo = isDevelopment;
  static const bool enableLogging = true;
  static const bool enablePerformanceMonitoring = isProduction;

  // Network Configuration
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;

  // Local Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String settingsKey = 'app_settings';
  static const String cacheKey = 'app_cache';
  static const String offlineDataKey = 'offline_data';

  // Notification Configuration
  static const String notificationChannelId = 'bac_study_notifications';
  static const String notificationChannelName = 'BAC Study Notifications';
  static const String notificationChannelDescription = 'Notifications for BAC Study App';

  // Analytics Events
  static const String eventAppOpen = 'app_open';
  static const String eventUserLogin = 'user_login';
  static const String eventUserRegister = 'user_register';
  static const String eventQuizStart = 'quiz_start';
  static const String eventQuizComplete = 'quiz_complete';
  static const String eventContentView = 'content_view';
  static const String eventContentDownload = 'content_download';
  static const String eventForumPost = 'forum_post';
  static const String eventSearchPerformed = 'search_performed';

  // Error Messages
  static const String errorNetworkConnection = 'خطأ في الاتصال بالإنترنت';
  static const String errorServerError = 'خطأ في الخادم';
  static const String errorUnknown = 'حدث خطأ غير متوقع';
  static const String errorInvalidCredentials = 'بيانات الدخول غير صحيحة';
  static const String errorUserNotFound = 'المستخدم غير موجود';
  static const String errorEmailAlreadyExists = 'البريد الإلكتروني مستخدم بالفعل';

  // Success Messages
  static const String successLogin = 'تم تسجيل الدخول بنجاح';
  static const String successRegister = 'تم إنشاء الحساب بنجاح';
  static const String successProfileUpdate = 'تم تحديث الملف الشخصي بنجاح';
  static const String successPasswordChange = 'تم تغيير كلمة المرور بنجاح';
  static const String successContentDownload = 'تم تحميل المحتوى بنجاح';

  // Validation Rules
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int maxBioLength = 500;
  static const int maxPostLength = 5000;
  static const int maxReplyLength = 2000;
}
