class ApiConfig {
  // Base URLs
  static const String baseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'http://localhost:3000',
  );
  
  static const String apiVersion = 'v1';
  static const String apiPrefix = '/api';
  
  // Full API URL
  static String get apiUrl => '$baseUrl$apiPrefix';
  
  // Authentication Endpoints
  static const String authLogin = '/auth/login';
  static const String authRegister = '/auth/register';
  static const String authRefresh = '/auth/refresh';
  static const String authLogout = '/auth/logout';
  static const String authProfile = '/auth/profile';
  
  // User Endpoints
  static const String userProfile = '/users/profile';
  static const String userUpdate = '/users/profile';
  static const String userProgress = '/users/progress';
  static const String userFavorites = '/users/favorites';
  
  // Subject Endpoints
  static const String subjects = '/subjects';
  static String subjectById(String id) => '/subjects/$id';
  static String subjectContent(String id) => '/subjects/$id/content';
  
  // Content Endpoints
  static const String content = '/content';
  static String contentById(String id) => '/content/$id';
  static const String contentSearch = '/content/search';
  static const String contentUpload = '/content/upload';
  static String contentDownload(String id) => '/content/$id/download';
  
  // Quiz Endpoints
  static const String quizzes = '/quizzes';
  static String quizById(String id) => '/quizzes/$id';
  static String quizStart(String id) => '/quizzes/$id/start';
  static String quizSubmit(String id) => '/quizzes/$id/submit';
  static const String quizResults = '/quizzes/results';
  static const String quizRecommendations = '/quizzes/recommendations';
  
  // Forum Endpoints
  static const String forumCategories = '/forum/categories';
  static const String forumPosts = '/forum/posts';
  static String forumPostById(String id) => '/forum/posts/$id';
  static String forumPostReplies(String id) => '/forum/posts/$id/replies';
  static const String forumCreatePost = '/forum/posts';
  static const String forumCreateReply = '/forum/replies';
  
  // Progress Endpoints
  static const String progressOverview = '/progress/overview';
  static const String progressSubjects = '/progress/subjects';
  static const String progressQuizzes = '/progress/quizzes';
  static const String progressStudyTime = '/progress/study-time';
  
  // File Upload Endpoints
  static const String uploadImage = '/upload/image';
  static const String uploadDocument = '/upload/document';
  static const String uploadVideo = '/upload/video';
  
  // Search Endpoints
  static const String searchContent = '/search/content';
  static const String searchUsers = '/search/users';
  static const String searchPosts = '/search/posts';
  
  // Notification Endpoints
  static const String notifications = '/notifications';
  static String notificationMarkRead(String id) => '/notifications/$id/read';
  static const String notificationSettings = '/notifications/settings';
  
  // Analytics Endpoints
  static const String analyticsEvent = '/analytics/event';
  static const String analyticsProgress = '/analytics/progress';
  
  // Admin Endpoints (for future use)
  static const String adminUsers = '/admin/users';
  static const String adminContent = '/admin/content';
  static const String adminReports = '/admin/reports';
  
  // Request Configuration
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Headers
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Accept-Language': 'ar,fr,en',
  };
  
  static Map<String, String> authHeaders(String token) => {
    ...defaultHeaders,
    'Authorization': 'Bearer $token',
  };
  
  // Response Status Codes
  static const int statusOk = 200;
  static const int statusCreated = 201;
  static const int statusNoContent = 204;
  static const int statusBadRequest = 400;
  static const int statusUnauthorized = 401;
  static const int statusForbidden = 403;
  static const int statusNotFound = 404;
  static const int statusConflict = 409;
  static const int statusInternalServerError = 500;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File Upload Limits
  static const int maxImageSize = 10 * 1024 * 1024; // 10MB
  static const int maxDocumentSize = 50 * 1024 * 1024; // 50MB
  static const int maxVideoSize = 100 * 1024 * 1024; // 100MB
  
  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 1);
  static const int maxCacheSize = 50 * 1024 * 1024; // 50MB
}
