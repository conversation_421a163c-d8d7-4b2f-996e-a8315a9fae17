import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/auth_service.dart';

/// Route guard that checks if user is authenticated
class AuthGuard {
  static String? checkAuth(BuildContext context, GoRouterState state) {
    final container = ProviderScope.containerOf(context);
    final authService = container.read(authServiceProvider);
    
    if (!authService.isAuthenticated) {
      // Redirect to login page
      return '/auth/login';
    }
    
    return null; // Allow navigation
  }
}

/// Route guard that checks if user is NOT authenticated (for login/register pages)
class GuestGuard {
  static String? checkGuest(BuildContext context, GoRouterState state) {
    final container = ProviderScope.containerOf(context);
    final authService = container.read(authServiceProvider);
    
    if (authService.isAuthenticated) {
      // Redirect to home page
      return '/';
    }
    
    return null; // Allow navigation
  }
}

/// Route guard that checks if user has completed onboarding
class OnboardingGuard {
  static String? checkOnboarding(BuildContext context, GoRouterState state) {
    final container = ProviderScope.containerOf(context);
    final authService = container.read(authServiceProvider);
    
    if (authService.isAuthenticated && !authService.hasCompletedOnboarding) {
      // Redirect to onboarding
      return '/onboarding';
    }
    
    return null; // Allow navigation
  }
}

/// Route guard that checks user role/permissions
class RoleGuard {
  static String? checkRole(
    BuildContext context, 
    GoRouterState state, 
    List<String> requiredRoles,
  ) {
    final container = ProviderScope.containerOf(context);
    final authService = container.read(authServiceProvider);
    
    if (!authService.isAuthenticated) {
      return '/auth/login';
    }
    
    final userRole = authService.currentUser?.role;
    if (userRole == null || !requiredRoles.contains(userRole)) {
      // Redirect to unauthorized page
      return '/unauthorized';
    }
    
    return null; // Allow navigation
  }
}

/// Route guard that checks if user has verified their email
class EmailVerificationGuard {
  static String? checkEmailVerification(BuildContext context, GoRouterState state) {
    final container = ProviderScope.containerOf(context);
    final authService = container.read(authServiceProvider);
    
    if (authService.isAuthenticated && !authService.isEmailVerified) {
      // Redirect to email verification page
      return '/auth/verify-email';
    }
    
    return null; // Allow navigation
  }
}

/// Route guard that checks if user has completed their profile
class ProfileCompletionGuard {
  static String? checkProfileCompletion(BuildContext context, GoRouterState state) {
    final container = ProviderScope.containerOf(context);
    final authService = container.read(authServiceProvider);
    
    if (authService.isAuthenticated && !authService.hasCompletedProfile) {
      // Redirect to profile completion page
      return '/profile/complete';
    }
    
    return null; // Allow navigation
  }
}

/// Composite guard that combines multiple guards
class CompositeGuard {
  static String? checkMultiple(
    BuildContext context,
    GoRouterState state,
    List<String? Function(BuildContext, GoRouterState)> guards,
  ) {
    for (final guard in guards) {
      final result = guard(context, state);
      if (result != null) {
        return result; // Return first redirect
      }
    }
    return null; // All guards passed
  }
}

/// Common guard combinations
class CommonGuards {
  /// Guard for authenticated users with complete profiles
  static String? authenticatedWithProfile(BuildContext context, GoRouterState state) {
    return CompositeGuard.checkMultiple(context, state, [
      AuthGuard.checkAuth,
      EmailVerificationGuard.checkEmailVerification,
      ProfileCompletionGuard.checkProfileCompletion,
    ]);
  }
  
  /// Guard for admin-only routes
  static String? adminOnly(BuildContext context, GoRouterState state) {
    return CompositeGuard.checkMultiple(context, state, [
      AuthGuard.checkAuth,
      (ctx, st) => RoleGuard.checkRole(ctx, st, ['admin']),
    ]);
  }
  
  /// Guard for teacher or admin routes
  static String? teacherOrAdmin(BuildContext context, GoRouterState state) {
    return CompositeGuard.checkMultiple(context, state, [
      AuthGuard.checkAuth,
      (ctx, st) => RoleGuard.checkRole(ctx, st, ['teacher', 'admin']),
    ]);
  }
}
