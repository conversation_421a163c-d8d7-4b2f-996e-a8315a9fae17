const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Simple health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'BAC Study App Backend is running!',
    timestamp: new Date().toISOString()
  });
});

// Simple API endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'Hello from BAC Study App API!',
    data: {
      app: 'BAC Study App',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    }
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 BAC Study App Backend running on port ${PORT}`);
  console.log(`📚 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 Test API: http://localhost:${PORT}/api/test`);
});

module.exports = app;
