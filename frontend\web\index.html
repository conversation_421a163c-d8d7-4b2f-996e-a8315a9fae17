<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق دراسة البكالوريا - BAC Study App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            max-width: 600px;
            width: 90%;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.2);
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        
        .status.loading {
            background: rgba(255, 193, 7, 0.3);
            border: 1px solid rgba(255, 193, 7, 0.5);
        }
        
        .api-test {
            margin: 2rem 0;
        }
        
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            transition: transform 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        .features {
            text-align: right;
            margin: 2rem 0;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .features li:before {
            content: "✓ ";
            color: #4CAF50;
            font-weight: bold;
            margin-left: 0.5rem;
        }
        
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .footer {
            margin-top: 2rem;
            font-size: 0.9rem;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 تطبيق دراسة البكالوريا</h1>
        <p class="subtitle">منصة تعليمية شاملة لطلاب البكالوريا الجزائرية</p>
        
        <div id="backend-status" class="status loading">
            <h3>حالة الخادم الخلفي</h3>
            <p>جاري التحقق من الاتصال... <span class="spinner"></span></p>
        </div>
        
        <div class="api-test">
            <button onclick="testAPI()">اختبار API</button>
            <button onclick="checkHealth()">فحص الصحة</button>
        </div>
        
        <div class="features">
            <h3>الميزات المتاحة:</h3>
            <ul>
                <li>نظام تقييم شخصي لتحديد نقاط الضعف</li>
                <li>مكتبة شاملة من المحتوى التعليمي</li>
                <li>امتحانات البكالوريا السابقة مع التصحيح</li>
                <li>منتديات نقاش للطلاب</li>
                <li>تتبع التقدم والإحصائيات</li>
                <li>دعم العمل بدون إنترنت</li>
                <li>واجهة متعددة اللغات (عربي، فرنسي، إنجليزي)</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>تطبيق BAC Study App - نسخة تطوير 1.0.0</p>
            <p>مصمم خصيصاً للطلاب الجزائريين</p>
        </div>
    </div>

    <script>
        // Test API connectivity
        async function checkHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();
                
                document.getElementById('backend-status').innerHTML = `
                    <h3>✅ الخادم الخلفي متصل</h3>
                    <p>الحالة: ${data.status}</p>
                    <p>الرسالة: ${data.message}</p>
                    <p>الوقت: ${new Date(data.timestamp).toLocaleString('ar-DZ')}</p>
                `;
                document.getElementById('backend-status').className = 'status success';
            } catch (error) {
                document.getElementById('backend-status').innerHTML = `
                    <h3>❌ خطأ في الاتصال</h3>
                    <p>لا يمكن الوصول إلى الخادم الخلفي</p>
                    <p>تأكد من تشغيل الخادم على المنفذ 3000</p>
                `;
                document.getElementById('backend-status').className = 'status';
            }
        }
        
        async function testAPI() {
            try {
                const response = await fetch('http://localhost:3000/api/test');
                const data = await response.json();
                
                alert(`✅ API يعمل بنجاح!\n\nالرسالة: ${data.message}\nالتطبيق: ${data.data.app}\nالإصدار: ${data.data.version}\nالبيئة: ${data.data.environment}`);
            } catch (error) {
                alert('❌ خطأ في اختبار API\n\nتأكد من تشغيل الخادم الخلفي');
            }
        }
        
        // Auto-check health on page load
        window.onload = function() {
            setTimeout(checkHealth, 1000);
        };
    </script>
</body>
</html>
