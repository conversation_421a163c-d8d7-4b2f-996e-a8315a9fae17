const { validationResult } = require('express-validator');
const authService = require('../services/auth.service');
const userService = require('../services/user.service');
const { AppError } = require('../middleware/error.middleware');
const logger = require('../utils/logger');

class AuthController {
  /**
   * Register a new user
   */
  async register(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(new AppError('Validation failed', 400, true, errors.array()));
      }

      const { firebase_token, full_name, wilaya, bac_year, bac_stream } = req.body;

      // Verify Firebase token and get user info
      const firebaseUser = await authService.verifyFirebaseToken(firebase_token);
      
      // Check if user already exists
      const existingUser = await userService.findByFirebaseUid(firebaseUser.uid);
      if (existingUser) {
        return next(new AppError('User already exists', 409));
      }

      // Create new user
      const userData = {
        firebase_uid: firebaseUser.uid,
        email: firebaseUser.email,
        full_name,
        wilaya,
        bac_year,
        bac_stream,
      };

      const user = await userService.create(userData);

      // Generate JWT tokens
      const tokens = await authService.generateTokens(user);

      logger.info(`New user registered: ${user.email}`);

      res.status(201).json({
        status: 'success',
        message: 'User registered successfully',
        data: {
          user: userService.sanitizeUser(user),
          tokens,
        },
      });
    } catch (error) {
      logger.error('Registration error:', error);
      next(error);
    }
  }

  /**
   * Login user
   */
  async login(req, res, next) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(new AppError('Validation failed', 400, true, errors.array()));
      }

      const { firebase_token } = req.body;

      // Verify Firebase token
      const firebaseUser = await authService.verifyFirebaseToken(firebase_token);
      
      // Get user from database
      const user = await userService.findByFirebaseUid(firebaseUser.uid);
      if (!user) {
        return next(new AppError('User not found. Please register first.', 404));
      }

      // Update last login
      await userService.updateLastLogin(user.id);

      // Generate JWT tokens
      const tokens = await authService.generateTokens(user);

      logger.info(`User logged in: ${user.email}`);

      res.status(200).json({
        status: 'success',
        message: 'Login successful',
        data: {
          user: userService.sanitizeUser(user),
          tokens,
        },
      });
    } catch (error) {
      logger.error('Login error:', error);
      next(error);
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(req, res, next) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(new AppError('Validation failed', 400, true, errors.array()));
      }

      const { refresh_token } = req.body;

      // Verify and decode refresh token
      const decoded = await authService.verifyRefreshToken(refresh_token);
      
      // Get user from database
      const user = await userService.findById(decoded.userId);
      if (!user) {
        return next(new AppError('User not found', 404));
      }

      // Generate new access token
      const accessToken = await authService.generateAccessToken(user);

      res.status(200).json({
        status: 'success',
        message: 'Token refreshed successfully',
        data: {
          access_token: accessToken,
        },
      });
    } catch (error) {
      logger.error('Token refresh error:', error);
      next(error);
    }
  }

  /**
   * Logout user
   */
  async logout(req, res, next) {
    try {
      const userId = req.user.id;

      // Invalidate refresh tokens (if you're storing them)
      await authService.invalidateUserTokens(userId);

      logger.info(`User logged out: ${req.user.email}`);

      res.status(200).json({
        status: 'success',
        message: 'Logout successful',
      });
    } catch (error) {
      logger.error('Logout error:', error);
      next(error);
    }
  }

  /**
   * Get current user profile
   */
  async getProfile(req, res, next) {
    try {
      const user = await userService.findById(req.user.id);
      if (!user) {
        return next(new AppError('User not found', 404));
      }

      res.status(200).json({
        status: 'success',
        data: {
          user: userService.sanitizeUser(user),
        },
      });
    } catch (error) {
      logger.error('Get profile error:', error);
      next(error);
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(req, res, next) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(new AppError('Validation failed', 400, true, errors.array()));
      }

      const userId = req.user.id;
      const updateData = req.body;

      // Remove sensitive fields that shouldn't be updated here
      delete updateData.firebase_uid;
      delete updateData.email;
      delete updateData.id;

      const updatedUser = await userService.update(userId, updateData);

      logger.info(`User profile updated: ${updatedUser.email}`);

      res.status(200).json({
        status: 'success',
        message: 'Profile updated successfully',
        data: {
          user: userService.sanitizeUser(updatedUser),
        },
      });
    } catch (error) {
      logger.error('Update profile error:', error);
      next(error);
    }
  }

  /**
   * Change password
   */
  async changePassword(req, res, next) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(new AppError('Validation failed', 400, true, errors.array()));
      }

      const { current_password, new_password } = req.body;
      const userId = req.user.id;

      // This would typically involve Firebase Admin SDK to change password
      // For now, we'll just log the action
      logger.info(`Password change requested for user: ${req.user.email}`);

      res.status(200).json({
        status: 'success',
        message: 'Password changed successfully',
      });
    } catch (error) {
      logger.error('Change password error:', error);
      next(error);
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(req, res, next) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(new AppError('Validation failed', 400, true, errors.array()));
      }

      const { email } = req.body;

      // Check if user exists
      const user = await userService.findByEmail(email);
      if (!user) {
        // Don't reveal if user exists or not for security
        return res.status(200).json({
          status: 'success',
          message: 'If an account with this email exists, a password reset link has been sent.',
        });
      }

      // Generate password reset token and send email
      await authService.sendPasswordResetEmail(user);

      logger.info(`Password reset requested for: ${email}`);

      res.status(200).json({
        status: 'success',
        message: 'If an account with this email exists, a password reset link has been sent.',
      });
    } catch (error) {
      logger.error('Password reset request error:', error);
      next(error);
    }
  }

  /**
   * Verify email
   */
  async verifyEmail(req, res, next) {
    try {
      const { token } = req.params;

      // Verify email verification token
      const result = await authService.verifyEmailToken(token);

      if (result.success) {
        res.status(200).json({
          status: 'success',
          message: 'Email verified successfully',
        });
      } else {
        return next(new AppError('Invalid or expired verification token', 400));
      }
    } catch (error) {
      logger.error('Email verification error:', error);
      next(error);
    }
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification(req, res, next) {
    try {
      const userId = req.user.id;
      const user = await userService.findById(userId);

      if (!user) {
        return next(new AppError('User not found', 404));
      }

      if (user.email_verified) {
        return next(new AppError('Email is already verified', 400));
      }

      await authService.sendEmailVerification(user);

      res.status(200).json({
        status: 'success',
        message: 'Verification email sent successfully',
      });
    } catch (error) {
      logger.error('Resend email verification error:', error);
      next(error);
    }
  }
}

module.exports = new AuthController();
