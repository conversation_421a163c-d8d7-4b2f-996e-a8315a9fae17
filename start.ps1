# BAC Study App - Startup Script for Windows PowerShell
# This script helps you start the application with proper environment setup

Write-Host "🎓 BAC Study App - Starting Development Environment" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Check if Docker is running
Write-Host "🔍 Checking Docker status..." -ForegroundColor Yellow
try {
    $dockerStatus = docker info 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker is not running"
    }
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    Write-Host "Download Docker Desktop from: https://www.docker.com/products/docker-desktop/" -ForegroundColor Yellow
    exit 1
}

# Check if .env file exists
Write-Host "🔍 Checking environment configuration..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  .env file not found. Creating from template..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "✅ Created .env file from template" -ForegroundColor Green
    Write-Host "📝 Please edit .env file with your configuration before continuing" -ForegroundColor Yellow
    Write-Host "   Minimum required changes:" -ForegroundColor Yellow
    Write-Host "   - Set JWT_SECRET to a strong random string" -ForegroundColor Yellow
    Write-Host "   - Configure Firebase credentials if needed" -ForegroundColor Yellow
    
    $continue = Read-Host "Do you want to continue with default values? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        Write-Host "Please edit .env file and run this script again." -ForegroundColor Yellow
        exit 0
    }
} else {
    Write-Host "✅ Environment file found" -ForegroundColor Green
}

# Create necessary directories
Write-Host "📁 Creating necessary directories..." -ForegroundColor Yellow
$directories = @(
    "backend/logs",
    "content/uploads",
    "content/temp",
    "database/backups"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "   Created: $dir" -ForegroundColor Gray
    }
}
Write-Host "✅ Directories ready" -ForegroundColor Green

# Function to check if port is available
function Test-Port {
    param([int]$Port)
    try {
        $listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $Port)
        $listener.Start()
        $listener.Stop()
        return $true
    } catch {
        return $false
    }
}

# Check required ports
Write-Host "🔍 Checking port availability..." -ForegroundColor Yellow
$ports = @(3000, 5432, 8080, 6379)
$portsInUse = @()

foreach ($port in $ports) {
    if (-not (Test-Port $port)) {
        $portsInUse += $port
    }
}

if ($portsInUse.Count -gt 0) {
    Write-Host "⚠️  The following ports are in use: $($portsInUse -join ', ')" -ForegroundColor Yellow
    Write-Host "   This might cause conflicts. Consider stopping other services." -ForegroundColor Yellow
    $continue = Read-Host "Do you want to continue anyway? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 0
    }
} else {
    Write-Host "✅ All required ports are available" -ForegroundColor Green
}

# Start the application
Write-Host "🚀 Starting BAC Study App..." -ForegroundColor Green
Write-Host "   This may take a few minutes on first run..." -ForegroundColor Yellow

try {
    # Build and start containers
    Write-Host "📦 Building and starting containers..." -ForegroundColor Yellow
    docker-compose up --build -d
    
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to start containers"
    }
    
    Write-Host "✅ Containers started successfully" -ForegroundColor Green
    
    # Wait for services to be ready
    Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    # Check service health
    Write-Host "🔍 Checking service health..." -ForegroundColor Yellow
    
    # Check database
    $dbReady = $false
    for ($i = 1; $i -le 30; $i++) {
        try {
            $dbStatus = docker-compose exec -T db pg_isready -U bac_user 2>$null
            if ($LASTEXITCODE -eq 0) {
                $dbReady = $true
                break
            }
        } catch {}
        Start-Sleep -Seconds 2
        Write-Host "   Waiting for database... ($i/30)" -ForegroundColor Gray
    }
    
    if ($dbReady) {
        Write-Host "✅ Database is ready" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Database might not be ready yet" -ForegroundColor Yellow
    }
    
    # Check backend
    $backendReady = $false
    for ($i = 1; $i -le 30; $i++) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000/health" -TimeoutSec 2 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                $backendReady = $true
                break
            }
        } catch {}
        Start-Sleep -Seconds 2
        Write-Host "   Waiting for backend... ($i/30)" -ForegroundColor Gray
    }
    
    if ($backendReady) {
        Write-Host "✅ Backend API is ready" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Backend might not be ready yet" -ForegroundColor Yellow
    }
    
    # Check frontend
    $frontendReady = $false
    for ($i = 1; $i -le 60; $i++) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 2 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                $frontendReady = $true
                break
            }
        } catch {}
        Start-Sleep -Seconds 2
        Write-Host "   Waiting for frontend... ($i/60)" -ForegroundColor Gray
    }
    
    if ($frontendReady) {
        Write-Host "✅ Frontend is ready" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Frontend might not be ready yet" -ForegroundColor Yellow
    }
    
    # Display success message and URLs
    Write-Host ""
    Write-Host "🎉 BAC Study App is now running!" -ForegroundColor Green
    Write-Host "=================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "📱 Frontend (Flutter Web): http://localhost:8080" -ForegroundColor Cyan
    Write-Host "🔧 Backend API: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "📚 API Documentation: http://localhost:3000/api-docs" -ForegroundColor Cyan
    Write-Host "🗄️  Database: localhost:5432 (user: bac_user, db: bac_db)" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📋 Useful Commands:" -ForegroundColor Yellow
    Write-Host "   View logs: docker-compose logs -f" -ForegroundColor Gray
    Write-Host "   Stop app: docker-compose down" -ForegroundColor Gray
    Write-Host "   Restart: docker-compose restart" -ForegroundColor Gray
    Write-Host "   Backend shell: docker-compose exec backend bash" -ForegroundColor Gray
    Write-Host "   Frontend shell: docker-compose exec frontend bash" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🔍 To monitor the application:" -ForegroundColor Yellow
    Write-Host "   docker-compose logs -f --tail=100" -ForegroundColor Gray
    Write-Host ""
    
    # Open browser (optional)
    $openBrowser = Read-Host "Do you want to open the app in your browser? (Y/n)"
    if ($openBrowser -ne "n" -and $openBrowser -ne "N") {
        Start-Process "http://localhost:8080"
        Start-Process "http://localhost:3000/api-docs"
    }
    
} catch {
    Write-Host "❌ Failed to start the application: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔍 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "   1. Check Docker Desktop is running" -ForegroundColor Gray
    Write-Host "   2. Check if ports 3000, 5432, 8080 are available" -ForegroundColor Gray
    Write-Host "   3. Check .env file configuration" -ForegroundColor Gray
    Write-Host "   4. View logs: docker-compose logs" -ForegroundColor Gray
    Write-Host ""
    exit 1
}

Write-Host "Happy coding! 🚀" -ForegroundColor Green
