{"name": "bac-study-app-backend", "version": "1.0.0", "description": "Backend API for BAC Study App - Educational platform for Algerian students", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js"}, "keywords": ["education", "bac", "algeria", "study", "api", "nodejs", "express"], "author": "BAC Study App Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "pg": "^8.11.3", "pg-pool": "^3.6.1", "redis": "^4.6.10", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "firebase-admin": "^11.11.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "pdf-parse": "^1.1.1", "node-cron": "^3.0.3", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "joi": "^17.11.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "moment": "^2.29.4", "nodemailer": "^6.9.7", "cloudinary": "^1.41.0", "socket.io": "^4.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/config/*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}