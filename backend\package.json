{"name": "bac-study-app-backend", "version": "1.0.0", "description": "Backend API for BAC Study App - Educational platform for Algerian students", "main": "src/server.js", "scripts": {"start": "node simple-server.js", "start:full": "node src/server.js", "dev": "nodemon simple-server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js"}, "keywords": ["education", "bac", "algeria", "study", "api", "nodejs", "express"], "author": "BAC Study App Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/config/*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}