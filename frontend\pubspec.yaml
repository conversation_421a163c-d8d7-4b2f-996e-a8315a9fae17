name: bac_study_app
description: "Educational mobile app for Algerian BAC students - Study materials, quizzes, and community features"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI and Material Design
  cupertino_icons: ^1.0.6
  material_design_icons_flutter: ^7.0.7296
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # Navigation
  go_router: ^12.1.3
  
  # HTTP and API
  http: ^1.1.2
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.3.0
  
  # Authentication
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  google_sign_in: ^6.1.6
  
  # File Handling
  file_picker: ^6.1.1
  path_provider: ^2.1.1
  permission_handler: ^11.1.0
  
  # PDF and Document Viewing
  flutter_pdfview: ^1.3.2
  syncfusion_flutter_pdfviewer: ^23.2.7
  
  # Video Player
  video_player: ^2.8.1
  youtube_player_flutter: ^8.1.2
  chewie: ^1.7.4
  
  # Image Handling
  image_picker: ^1.0.4
  image_cropper: ^5.0.1
  
  # Networking and Connectivity
  connectivity_plus: ^5.0.2
  internet_connection_checker: ^1.0.0+1
  
  # Utilities
  intl: ^0.19.0
  uuid: ^4.2.1
  url_launcher: ^6.2.2
  share_plus: ^7.2.1
  package_info_plus: ^4.2.0
  device_info_plus: ^9.1.1
  
  # UI Enhancements
  flutter_staggered_grid_view: ^0.7.0
  pull_to_refresh: ^2.0.0
  infinite_scroll_pagination: ^4.0.0
  flutter_rating_bar: ^4.0.1
  
  # Charts and Analytics
  fl_chart: ^0.65.0
  
  # Notifications
  flutter_local_notifications: ^16.3.0
  firebase_messaging: ^14.7.9
  
  # Real-time Communication
  socket_io_client: ^2.0.3+1
  
  # Security
  crypto: ^3.0.3
  encrypt: ^5.0.1
  
  # Development and Debugging
  logger: ^2.0.2+1
  
  # Localization
  flutter_localizations:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
  
  # Linting and Analysis
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0
  
  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter
  
  # Icons and Assets
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true
  
  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
  
  # Fonts
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
    
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300

# Flutter Launcher Icons Configuration
flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48
