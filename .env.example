# BAC Study App Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=****************************************/bac_db

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key-change-in-production-make-it-long-and-random
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour Firebase private key here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com

# =============================================================================
# REDIS CONFIGURATION (Optional)
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:3000
FRONTEND_URL=http://localhost:8080

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
SESSION_SECRET=your-session-secret-change-in-production
CORS_ORIGINS=http://localhost:8080,http://localhost:3000

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
# Cloudinary (for production file storage)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FILE=logs/app.log

# =============================================================================
# EXTERNAL APIS (Optional)
# =============================================================================
# YouTube API (for video embedding)
YOUTUBE_API_KEY=your-youtube-api-key

# Google Analytics (for app analytics)
GOOGLE_ANALYTICS_ID=your-ga-tracking-id

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Set to true to enable development features
DEBUG=true
ENABLE_SWAGGER=true
ENABLE_CORS=true

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================
# Uncomment and configure for production deployment

# SSL Configuration
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/private.key

# Database SSL (for production)
# DATABASE_SSL=true

# Rate limiting (requests per window)
# RATE_LIMIT_WINDOW_MS=900000
# RATE_LIMIT_MAX_REQUESTS=100

# Session configuration
# SESSION_SECURE=true
# SESSION_SAME_SITE=strict

# =============================================================================
# MONITORING AND ANALYTICS
# =============================================================================
# Sentry (for error tracking)
# SENTRY_DSN=your-sentry-dsn

# New Relic (for performance monitoring)
# NEW_RELIC_LICENSE_KEY=your-new-relic-license-key
# NEW_RELIC_APP_NAME=bac-study-app

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Database backup settings
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30
# BACKUP_STORAGE_PATH=/backups

# =============================================================================
# CONTENT DELIVERY
# =============================================================================
# CDN Configuration (for static assets)
# CDN_URL=https://your-cdn-domain.com
# CDN_API_KEY=your-cdn-api-key

# =============================================================================
# SOCIAL MEDIA INTEGRATION
# =============================================================================
# Facebook App (for social login)
# FACEBOOK_APP_ID=your-facebook-app-id
# FACEBOOK_APP_SECRET=your-facebook-app-secret

# Google OAuth (for social login)
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret

# =============================================================================
# PAYMENT INTEGRATION (Future feature)
# =============================================================================
# Stripe (for premium features)
# STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
# STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
# STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# =============================================================================
# MOBILE APP CONFIGURATION
# =============================================================================
# App Store Connect (for iOS)
# APP_STORE_CONNECT_API_KEY=your-app-store-connect-api-key

# Google Play Console (for Android)
# GOOGLE_PLAY_SERVICE_ACCOUNT_KEY=your-service-account-key.json

# =============================================================================
# LOCALIZATION
# =============================================================================
# Default language and supported languages
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,fr,en

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable features
ENABLE_FORUM=true
ENABLE_VIDEO_STREAMING=true
ENABLE_OFFLINE_MODE=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=true
ENABLE_CRASH_REPORTING=true
