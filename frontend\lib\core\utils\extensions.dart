import 'package:flutter/material.dart';

// String extensions
extension StringExtensions on String {
  // Check if string is empty or null
  bool get isNullOrEmpty => isEmpty;

  // Check if string is not empty
  bool get isNotNullOrEmpty => isNotEmpty;

  // Capitalize first letter
  String get capitalize {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1)}';
  }

  // Convert to title case
  String get toTitleCase {
    return split(' ').map((word) => word.capitalize).join(' ');
  }

  // Remove extra spaces
  String get removeExtraSpaces {
    return replaceAll(RegExp(r'\s+'), ' ').trim();
  }

  // Check if string is a valid email
  bool get isValidEmail {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(this);
  }

  // Check if string is a valid phone number (Algerian)
  bool get isValidAlgerianPhone {
    final cleanNumber = replaceAll(RegExp(r'[\s\-\(\)]'), '');
    return RegExp(r'^(0|\+213)[567]\d{8}$').hasMatch(cleanNumber);
  }

  // Convert Arabic digits to English
  String get toEnglishDigits {
    const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    String result = this;
    for (int i = 0; i < arabicDigits.length; i++) {
      result = result.replaceAll(arabicDigits[i], englishDigits[i]);
    }
    return result;
  }

  // Convert English digits to Arabic
  String get toArabicDigits {
    const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    String result = this;
    for (int i = 0; i < englishDigits.length; i++) {
      result = result.replaceAll(englishDigits[i], arabicDigits[i]);
    }
    return result;
  }

  // Truncate string with ellipsis
  String truncate(int maxLength) {
    if (length <= maxLength) return this;
    return '${substring(0, maxLength)}...';
  }

  // Check if string contains Arabic characters
  bool get hasArabicCharacters {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(this);
  }

  // Get initials from name
  String get initials {
    final words = trim().split(' ');
    if (words.length >= 2) {
      return '${words.first[0]}${words.last[0]}'.toUpperCase();
    } else if (words.isNotEmpty) {
      return words.first[0].toUpperCase();
    }
    return '';
  }
}

// DateTime extensions
extension DateTimeExtensions on DateTime {
  // Check if date is today
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  // Check if date is yesterday
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year && month == yesterday.month && day == yesterday.day;
  }

  // Check if date is tomorrow
  bool get isTomorrow {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return year == tomorrow.year && month == tomorrow.month && day == tomorrow.day;
  }

  // Check if date is this week
  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return isAfter(startOfWeek.subtract(const Duration(days: 1))) && 
           isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  // Get time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? 'منذ سنة' : 'منذ $years سنوات';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? 'منذ شهر' : 'منذ $months أشهر';
    } else if (difference.inDays > 0) {
      return difference.inDays == 1 ? 'منذ يوم' : 'منذ ${difference.inDays} أيام';
    } else if (difference.inHours > 0) {
      return difference.inHours == 1 ? 'منذ ساعة' : 'منذ ${difference.inHours} ساعات';
    } else if (difference.inMinutes > 0) {
      return difference.inMinutes == 1 ? 'منذ دقيقة' : 'منذ ${difference.inMinutes} دقائق';
    } else {
      return 'الآن';
    }
  }

  // Get start of day
  DateTime get startOfDay {
    return DateTime(year, month, day);
  }

  // Get end of day
  DateTime get endOfDay {
    return DateTime(year, month, day, 23, 59, 59, 999);
  }

  // Get start of week (Monday)
  DateTime get startOfWeek {
    return subtract(Duration(days: weekday - 1)).startOfDay;
  }

  // Get end of week (Sunday)
  DateTime get endOfWeek {
    return add(Duration(days: 7 - weekday)).endOfDay;
  }
}

// Duration extensions
extension DurationExtensions on Duration {
  // Format duration as string
  String get formatted {
    final hours = inHours;
    final minutes = inMinutes.remainder(60);
    final seconds = inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  // Get human readable duration
  String get humanReadable {
    if (inDays > 0) {
      return inDays == 1 ? 'يوم واحد' : '$inDays أيام';
    } else if (inHours > 0) {
      return inHours == 1 ? 'ساعة واحدة' : '$inHours ساعات';
    } else if (inMinutes > 0) {
      return inMinutes == 1 ? 'دقيقة واحدة' : '$inMinutes دقائق';
    } else {
      return inSeconds == 1 ? 'ثانية واحدة' : '$inSeconds ثواني';
    }
  }
}

// List extensions
extension ListExtensions<T> on List<T> {
  // Get random element
  T? get random {
    if (isEmpty) return null;
    return this[(length * (DateTime.now().millisecondsSinceEpoch % 1000) / 1000).floor()];
  }

  // Check if list is null or empty
  bool get isNullOrEmpty => isEmpty;

  // Check if list is not null or empty
  bool get isNotNullOrEmpty => isNotEmpty;

  // Get element at index safely
  T? elementAtOrNull(int index) {
    if (index < 0 || index >= length) return null;
    return this[index];
  }

  // Add element if not null
  void addIfNotNull(T? element) {
    if (element != null) add(element);
  }

  // Remove duplicates
  List<T> get unique {
    return toSet().toList();
  }

  // Chunk list into smaller lists
  List<List<T>> chunk(int size) {
    final chunks = <List<T>>[];
    for (int i = 0; i < length; i += size) {
      chunks.add(sublist(i, i + size > length ? length : i + size));
    }
    return chunks;
  }
}

// BuildContext extensions
extension BuildContextExtensions on BuildContext {
  // Theme shortcuts
  ThemeData get theme => Theme.of(this);
  ColorScheme get colorScheme => theme.colorScheme;
  TextTheme get textTheme => theme.textTheme;

  // MediaQuery shortcuts
  MediaQueryData get mediaQuery => MediaQuery.of(this);
  Size get screenSize => mediaQuery.size;
  double get screenWidth => screenSize.width;
  double get screenHeight => screenSize.height;
  EdgeInsets get padding => mediaQuery.padding;
  EdgeInsets get viewInsets => mediaQuery.viewInsets;

  // Navigation shortcuts
  NavigatorState get navigator => Navigator.of(this);
  void pop([dynamic result]) => navigator.pop(result);
  Future<T?> push<T>(Route<T> route) => navigator.push(route);
  Future<T?> pushNamed<T>(String routeName, {Object? arguments}) => 
      navigator.pushNamed(routeName, arguments: arguments);

  // Scaffold shortcuts
  ScaffoldMessengerState get scaffoldMessenger => ScaffoldMessenger.of(this);
  void showSnackBar(SnackBar snackBar) => scaffoldMessenger.showSnackBar(snackBar);

  // Show success message
  void showSuccessMessage(String message) {
    showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: colorScheme.primary,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Show error message
  void showErrorMessage(String message) {
    showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: colorScheme.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Check if device is mobile
  bool get isMobile => screenWidth < 600;

  // Check if device is tablet
  bool get isTablet => screenWidth >= 600 && screenWidth < 1200;

  // Check if device is desktop
  bool get isDesktop => screenWidth >= 1200;

  // Get responsive value
  T responsive<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop && desktop != null) return desktop;
    if (isTablet && tablet != null) return tablet;
    return mobile;
  }
}
