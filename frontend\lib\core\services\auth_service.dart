import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';
import '../utils/logger.dart';
import '../../features/auth/models/user_model.dart';

class AuthService extends StateNotifier<AuthState> {
  final FirebaseAuth _firebaseAuth;
  final SharedPreferences _prefs;

  AuthService(this._firebaseAuth, this._prefs) : super(const AuthState()) {
    _initializeAuth();
  }

  // Initialize authentication state
  Future<void> _initializeAuth() async {
    try {
      // Check for stored token
      final token = _prefs.getString('access_token');
      final refreshToken = _prefs.getString('refresh_token');
      final userJson = _prefs.getString('user_data');

      if (token != null && userJson != null) {
        final user = UserModel.fromJson(userJson);
        state = state.copyWith(
          isAuthenticated: true,
          user: user,
          accessToken: token,
          refreshToken: refreshToken,
        );
      }

      // Listen to Firebase auth changes
      _firebaseAuth.authStateChanges().listen(_onAuthStateChanged);
    } catch (e) {
      AppLogger.error('Failed to initialize auth', e);
    }
  }

  void _onAuthStateChanged(User? firebaseUser) {
    if (firebaseUser == null && state.isAuthenticated) {
      // User signed out from Firebase
      logout();
    }
  }

  // Login with email and password
  Future<void> loginWithEmail(String email, String password) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Sign in with Firebase
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Get Firebase ID token
        final idToken = await credential.user!.getIdToken();
        
        // Exchange for backend tokens
        await _exchangeFirebaseToken(idToken);
      }
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: _getFirebaseErrorMessage(e.code),
      );
      throw AuthException(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Login failed. Please try again.',
      );
      throw AuthException('Login failed. Please try again.');
    }
  }

  // Register with email and password
  Future<void> registerWithEmail({
    required String email,
    required String password,
    required String fullName,
    required String wilaya,
    required int bacYear,
    required String bacStream,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Create Firebase account
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Update Firebase profile
        await credential.user!.updateDisplayName(fullName);
        
        // Get Firebase ID token
        final idToken = await credential.user!.getIdToken();
        
        // Register with backend
        await _registerWithBackend(
          idToken: idToken,
          fullName: fullName,
          wilaya: wilaya,
          bacYear: bacYear,
          bacStream: bacStream,
        );
      }
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: _getFirebaseErrorMessage(e.code),
      );
      throw AuthException(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Registration failed. Please try again.',
      );
      throw AuthException('Registration failed. Please try again.');
    }
  }

  // Exchange Firebase token for backend tokens
  Future<void> _exchangeFirebaseToken(String firebaseToken) async {
    // This would make an API call to your backend
    // For now, we'll simulate it
    await Future.delayed(const Duration(seconds: 1));
    
    // Simulate successful token exchange
    const accessToken = 'mock_access_token';
    const refreshToken = 'mock_refresh_token';
    
    final user = UserModel(
      id: 'user_123',
      email: _firebaseAuth.currentUser?.email ?? '',
      fullName: _firebaseAuth.currentUser?.displayName ?? '',
      wilaya: 'الجزائر',
      bacYear: 2024,
      bacStream: 'علوم تجريبية',
      createdAt: DateTime.now(),
    );

    // Store tokens and user data
    await _storeAuthData(accessToken, refreshToken, user);

    state = state.copyWith(
      isAuthenticated: true,
      isLoading: false,
      user: user,
      accessToken: accessToken,
      refreshToken: refreshToken,
      error: null,
    );
  }

  // Register with backend
  Future<void> _registerWithBackend({
    required String idToken,
    required String fullName,
    required String wilaya,
    required int bacYear,
    required String bacStream,
  }) async {
    // This would make an API call to your backend registration endpoint
    await Future.delayed(const Duration(seconds: 1));
    
    // Simulate successful registration
    await _exchangeFirebaseToken(idToken);
  }

  // Refresh access token
  Future<void> refreshToken() async {
    try {
      final currentRefreshToken = state.refreshToken;
      if (currentRefreshToken == null) {
        throw AuthException('No refresh token available');
      }

      // This would make an API call to refresh the token
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Simulate successful token refresh
      const newAccessToken = 'new_mock_access_token';
      
      await _prefs.setString('access_token', newAccessToken);
      
      state = state.copyWith(accessToken: newAccessToken);
    } catch (e) {
      AppLogger.error('Token refresh failed', e);
      await logout();
      throw AuthException('Session expired. Please login again.');
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      // Sign out from Firebase
      await _firebaseAuth.signOut();
      
      // Clear stored data
      await _clearAuthData();
      
      state = const AuthState();
    } catch (e) {
      AppLogger.error('Logout failed', e);
    }
  }

  // Store authentication data
  Future<void> _storeAuthData(String accessToken, String refreshToken, UserModel user) async {
    await _prefs.setString('access_token', accessToken);
    await _prefs.setString('refresh_token', refreshToken);
    await _prefs.setString('user_data', user.toJson());
  }

  // Clear authentication data
  Future<void> _clearAuthData() async {
    await _prefs.remove('access_token');
    await _prefs.remove('refresh_token');
    await _prefs.remove('user_data');
  }

  // Get Firebase error message
  String _getFirebaseErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak.';
      case 'invalid-email':
        return 'Invalid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      default:
        return 'An error occurred. Please try again.';
    }
  }

  // Getters
  bool get isAuthenticated => state.isAuthenticated;
  UserModel? get currentUser => state.user;
  String? get accessToken => state.accessToken;
  bool get isEmailVerified => _firebaseAuth.currentUser?.emailVerified ?? false;
  bool get hasCompletedOnboarding => state.user?.hasCompletedOnboarding ?? false;
  bool get hasCompletedProfile => state.user?.isProfileComplete ?? false;
}

// Auth state
class AuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final UserModel? user;
  final String? accessToken;
  final String? refreshToken;
  final String? error;

  const AuthState({
    this.isAuthenticated = false,
    this.isLoading = false,
    this.user,
    this.accessToken,
    this.refreshToken,
    this.error,
  });

  AuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    UserModel? user,
    String? accessToken,
    String? refreshToken,
    String? error,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      error: error ?? this.error,
    );
  }
}

// Auth exception
class AuthException implements Exception {
  final String message;
  AuthException(this.message);
  
  @override
  String toString() => 'AuthException: $message';
}

// Providers
final firebaseAuthProvider = Provider<FirebaseAuth>((ref) => FirebaseAuth.instance);

final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be initialized');
});

final authServiceProvider = StateNotifierProvider<AuthService, AuthState>((ref) {
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  final prefs = ref.watch(sharedPreferencesProvider);
  return AuthService(firebaseAuth, prefs);
});
