const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../database/connection');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { generateJWT, generateRefreshToken, verifyFirebaseToken } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - firebase_uid
 *         - email
 *         - full_name
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         firebase_uid:
 *           type: string
 *         email:
 *           type: string
 *           format: email
 *         full_name:
 *           type: string
 *         wilaya:
 *           type: string
 *         bac_year:
 *           type: integer
 *         bac_stream:
 *           type: string
 *         created_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firebase_token
 *               - full_name
 *               - wilaya
 *               - bac_year
 *               - bac_stream
 *             properties:
 *               firebase_token:
 *                 type: string
 *               full_name:
 *                 type: string
 *               wilaya:
 *                 type: string
 *               bac_year:
 *                 type: integer
 *               bac_stream:
 *                 type: string
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: Invalid input data
 *       409:
 *         description: User already exists
 */
router.post('/register', [
  body('firebase_token').notEmpty().withMessage('Firebase token is required'),
  body('full_name').isLength({ min: 2, max: 255 }).withMessage('Full name must be between 2 and 255 characters'),
  body('wilaya').isLength({ min: 2, max: 100 }).withMessage('Wilaya is required'),
  body('bac_year').isInt({ min: 2020, max: 2030 }).withMessage('BAC year must be between 2020 and 2030'),
  body('bac_stream').isIn(['Sciences', 'Lettres', 'Mathématiques', 'Gestion', 'Technique']).withMessage('Invalid BAC stream'),
], catchAsync(async (req, res, next) => {
  // Check validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, true, errors.array()));
  }

  const { firebase_token, full_name, wilaya, bac_year, bac_stream } = req.body;

  try {
    // Verify Firebase token
    const firebaseUser = await verifyFirebaseToken(firebase_token);
    
    // Check if user already exists
    const existingUser = await query(
      'SELECT id FROM users WHERE firebase_uid = $1 OR email = $2',
      [firebaseUser.uid, firebaseUser.email]
    );

    if (existingUser.rows.length > 0) {
      return next(new AppError('User already exists', 409));
    }

    // Create new user
    const newUser = await query(
      `INSERT INTO users (firebase_uid, email, full_name, wilaya, bac_year, bac_stream)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING id, firebase_uid, email, full_name, wilaya, bac_year, bac_stream, created_at`,
      [firebaseUser.uid, firebaseUser.email, full_name, wilaya, bac_year, bac_stream]
    );

    const user = newUser.rows[0];

    // Generate JWT tokens
    const accessToken = generateJWT({
      userId: user.id,
      firebaseUid: user.firebase_uid,
      email: user.email
    });

    const refreshToken = generateRefreshToken({
      userId: user.id,
      firebaseUid: user.firebase_uid
    });

    logger.info(`New user registered: ${user.email}`);

    res.status(201).json({
      status: 'success',
      message: 'User registered successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          wilaya: user.wilaya,
          bac_year: user.bac_year,
          bac_stream: user.bac_stream,
          created_at: user.created_at
        },
        tokens: {
          access_token: accessToken,
          refresh_token: refreshToken
        }
      }
    });

  } catch (error) {
    logger.error('Registration error:', error);
    return next(new AppError('Registration failed', 500));
  }
}));

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Login user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firebase_token
 *             properties:
 *               firebase_token:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 *       404:
 *         description: User not found
 */
router.post('/login', [
  body('firebase_token').notEmpty().withMessage('Firebase token is required'),
], catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, true, errors.array()));
  }

  const { firebase_token } = req.body;

  try {
    // Verify Firebase token
    const firebaseUser = await verifyFirebaseToken(firebase_token);
    
    // Get user from database
    const userResult = await query(
      'SELECT * FROM users WHERE firebase_uid = $1',
      [firebaseUser.uid]
    );

    if (userResult.rows.length === 0) {
      return next(new AppError('User not found. Please register first.', 404));
    }

    const user = userResult.rows[0];

    // Generate JWT tokens
    const accessToken = generateJWT({
      userId: user.id,
      firebaseUid: user.firebase_uid,
      email: user.email
    });

    const refreshToken = generateRefreshToken({
      userId: user.id,
      firebaseUid: user.firebase_uid
    });

    // Update last login
    await query(
      'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [user.id]
    );

    logger.info(`User logged in: ${user.email}`);

    res.status(200).json({
      status: 'success',
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          wilaya: user.wilaya,
          bac_year: user.bac_year,
          bac_stream: user.bac_stream
        },
        tokens: {
          access_token: accessToken,
          refresh_token: refreshToken
        }
      }
    });

  } catch (error) {
    logger.error('Login error:', error);
    return next(new AppError('Login failed', 401));
  }
}));

/**
 * @swagger
 * /api/auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refresh_token
 *             properties:
 *               refresh_token:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 */
router.post('/refresh', [
  body('refresh_token').notEmpty().withMessage('Refresh token is required'),
], catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, true, errors.array()));
  }

  const { refresh_token } = req.body;

  try {
    // Verify refresh token
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(refresh_token, require('../config/config').jwt.secret);
    
    // Get user from database
    const userResult = await query(
      'SELECT * FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (userResult.rows.length === 0) {
      return next(new AppError('User not found', 404));
    }

    const user = userResult.rows[0];

    // Generate new access token
    const accessToken = generateJWT({
      userId: user.id,
      firebaseUid: user.firebase_uid,
      email: user.email
    });

    res.status(200).json({
      status: 'success',
      message: 'Token refreshed successfully',
      data: {
        access_token: accessToken
      }
    });

  } catch (error) {
    logger.error('Token refresh error:', error);
    return next(new AppError('Invalid refresh token', 401));
  }
}));

module.exports = router;
