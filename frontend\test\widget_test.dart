import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:bac_study_app/main.dart';

void main() {
  group('BAC Study App Widget Tests', () {
    testWidgets('App should start without crashing', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        const ProviderScope(
          child: BACStudyApp(),
        ),
      );

      // Verify that the app starts without throwing an exception
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('Error app should display error message', (WidgetTester tester) async {
      const errorMessage = 'Test error message';
      
      // Build the error app
      await tester.pumpWidget(
        const ErrorApp(error: errorMessage),
      );

      // Verify that error message is displayed
      expect(find.text('خطأ في تشغيل التطبيق'), findsOneWidget);
      expect(find.text('عذراً، حدث خطأ أثناء تشغيل التطبيق. يرجى إعادة تشغيل التطبيق.'), findsOneWidget);
      expect(find.text('إغلاق التطبيق'), findsOneWidget);
    });

    testWidgets('Error app should show error details in development mode', (WidgetTester tester) async {
      const errorMessage = 'Test error message';
      
      // Build the error app
      await tester.pumpWidget(
        const ErrorApp(error: errorMessage),
      );

      // In development mode, error details should be visible
      // This test assumes AppConfig.isDevelopment is true in test environment
      expect(find.text('Error Details (Development Mode):'), findsOneWidget);
      expect(find.text(errorMessage), findsOneWidget);
    });

    testWidgets('Close button should be present in error app', (WidgetTester tester) async {
      const errorMessage = 'Test error message';
      
      await tester.pumpWidget(
        const ErrorApp(error: errorMessage),
      );

      // Find the close button
      final closeButton = find.text('إغلاق التطبيق');
      expect(closeButton, findsOneWidget);

      // Verify it's an ElevatedButton
      expect(find.byType(ElevatedButton), findsOneWidget);
    });
  });

  group('App Configuration Tests', () {
    test('App config should have correct default values', () {
      // These tests would import and test AppConfig
      // For now, we'll add placeholder tests
      expect(true, isTrue); // Placeholder
    });

    test('Supported locales should include Arabic', () {
      // Test that Arabic locale is supported
      expect(true, isTrue); // Placeholder
    });

    test('BAC streams should include all Algerian streams', () {
      // Test that all required BAC streams are present
      expect(true, isTrue); // Placeholder
    });
  });

  group('Theme Tests', () {
    testWidgets('App should use Material Design 3', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: BACStudyApp(),
        ),
      );

      // Find MaterialApp
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      
      // Verify Material Design 3 is used
      expect(materialApp.theme?.useMaterial3, isTrue);
    });

    testWidgets('App should support both light and dark themes', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: BACStudyApp(),
        ),
      );

      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      
      // Verify both themes are set
      expect(materialApp.theme, isNotNull);
      expect(materialApp.darkTheme, isNotNull);
    });
  });

  group('Localization Tests', () {
    testWidgets('App should support Arabic localization', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: BACStudyApp(),
        ),
      );

      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      
      // Check if Arabic is in supported locales
      final arabicLocale = materialApp.supportedLocales.firstWhere(
        (locale) => locale.languageCode == 'ar',
        orElse: () => const Locale('en'),
      );
      
      expect(arabicLocale.languageCode, equals('ar'));
    });

    testWidgets('App should support French localization', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: BACStudyApp(),
        ),
      );

      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      
      // Check if French is in supported locales
      final frenchLocale = materialApp.supportedLocales.firstWhere(
        (locale) => locale.languageCode == 'fr',
        orElse: () => const Locale('en'),
      );
      
      expect(frenchLocale.languageCode, equals('fr'));
    });
  });

  group('Navigation Tests', () {
    testWidgets('App should use GoRouter for navigation', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: BACStudyApp(),
        ),
      );

      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      
      // Verify router configuration is set
      expect(materialApp.routerConfig, isNotNull);
    });
  });

  group('Provider Tests', () {
    testWidgets('App should be wrapped in ProviderScope', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: BACStudyApp(),
        ),
      );

      // Verify ProviderScope is present
      expect(find.byType(ProviderScope), findsOneWidget);
    });
  });

  group('Accessibility Tests', () {
    testWidgets('Error app should be accessible', (WidgetTester tester) async {
      const errorMessage = 'Test error message';
      
      await tester.pumpWidget(
        const ErrorApp(error: errorMessage),
      );

      // Check for semantic labels and accessibility
      expect(tester.getSemantics(find.text('خطأ في تشغيل التطبيق')), isNotNull);
      expect(tester.getSemantics(find.text('إغلاق التطبيق')), isNotNull);
    });

    testWidgets('App should have proper text scaling', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: BACStudyApp(),
        ),
      );

      // Verify MediaQuery is used for text scaling
      expect(find.byType(MediaQuery), findsOneWidget);
    });
  });

  group('Performance Tests', () {
    testWidgets('App should render within reasonable time', (WidgetTester tester) async {
      final stopwatch = Stopwatch()..start();
      
      await tester.pumpWidget(
        const ProviderScope(
          child: BACStudyApp(),
        ),
      );
      
      stopwatch.stop();
      
      // App should render within 1 second
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
    });
  });
}
