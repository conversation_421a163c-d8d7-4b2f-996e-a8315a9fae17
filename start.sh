#!/bin/bash

# BAC Study App - Startup Script for Linux/macOS
# This script helps you start the application with proper environment setup

echo "🎓 BAC Study App - Starting Development Environment"
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# Check if Docker is running
echo -e "${YELLOW}🔍 Checking Docker status...${NC}"
if ! docker info >/dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    echo -e "${YELLOW}Install Docker from: https://docs.docker.com/get-docker/${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker is running${NC}"

# Check if .env file exists
echo -e "${YELLOW}🔍 Checking environment configuration...${NC}"
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️  .env file not found. Creating from template...${NC}"
    cp .env.example .env
    echo -e "${GREEN}✅ Created .env file from template${NC}"
    echo -e "${YELLOW}📝 Please edit .env file with your configuration before continuing${NC}"
    echo -e "${YELLOW}   Minimum required changes:${NC}"
    echo -e "${YELLOW}   - Set JWT_SECRET to a strong random string${NC}"
    echo -e "${YELLOW}   - Configure Firebase credentials if needed${NC}"
    
    read -p "Do you want to continue with default values? (y/N): " continue_choice
    if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Please edit .env file and run this script again.${NC}"
        exit 0
    fi
else
    echo -e "${GREEN}✅ Environment file found${NC}"
fi

# Create necessary directories
echo -e "${YELLOW}📁 Creating necessary directories...${NC}"
directories=(
    "backend/logs"
    "content/uploads"
    "content/temp"
    "database/backups"
)

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo -e "${GRAY}   Created: $dir${NC}"
    fi
done
echo -e "${GREEN}✅ Directories ready${NC}"

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1
    else
        return 0
    fi
}

# Check required ports
echo -e "${YELLOW}🔍 Checking port availability...${NC}"
ports=(3000 5432 8080 6379)
ports_in_use=()

for port in "${ports[@]}"; do
    if ! check_port $port; then
        ports_in_use+=($port)
    fi
done

if [ ${#ports_in_use[@]} -gt 0 ]; then
    echo -e "${YELLOW}⚠️  The following ports are in use: ${ports_in_use[*]}${NC}"
    echo -e "${YELLOW}   This might cause conflicts. Consider stopping other services.${NC}"
    read -p "Do you want to continue anyway? (y/N): " continue_choice
    if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
        exit 0
    fi
else
    echo -e "${GREEN}✅ All required ports are available${NC}"
fi

# Start the application
echo -e "${GREEN}🚀 Starting BAC Study App...${NC}"
echo -e "${YELLOW}   This may take a few minutes on first run...${NC}"

# Build and start containers
echo -e "${YELLOW}📦 Building and starting containers...${NC}"
if ! docker-compose up --build -d; then
    echo -e "${RED}❌ Failed to start containers${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Containers started successfully${NC}"

# Wait for services to be ready
echo -e "${YELLOW}⏳ Waiting for services to be ready...${NC}"
sleep 10

# Check service health
echo -e "${YELLOW}🔍 Checking service health...${NC}"

# Check database
db_ready=false
for i in {1..30}; do
    if docker-compose exec -T db pg_isready -U bac_user >/dev/null 2>&1; then
        db_ready=true
        break
    fi
    sleep 2
    echo -e "${GRAY}   Waiting for database... ($i/30)${NC}"
done

if [ "$db_ready" = true ]; then
    echo -e "${GREEN}✅ Database is ready${NC}"
else
    echo -e "${YELLOW}⚠️  Database might not be ready yet${NC}"
fi

# Check backend
backend_ready=false
for i in {1..30}; do
    if curl -s http://localhost:3000/health >/dev/null 2>&1; then
        backend_ready=true
        break
    fi
    sleep 2
    echo -e "${GRAY}   Waiting for backend... ($i/30)${NC}"
done

if [ "$backend_ready" = true ]; then
    echo -e "${GREEN}✅ Backend API is ready${NC}"
else
    echo -e "${YELLOW}⚠️  Backend might not be ready yet${NC}"
fi

# Check frontend
frontend_ready=false
for i in {1..60}; do
    if curl -s http://localhost:8080 >/dev/null 2>&1; then
        frontend_ready=true
        break
    fi
    sleep 2
    echo -e "${GRAY}   Waiting for frontend... ($i/60)${NC}"
done

if [ "$frontend_ready" = true ]; then
    echo -e "${GREEN}✅ Frontend is ready${NC}"
else
    echo -e "${YELLOW}⚠️  Frontend might not be ready yet${NC}"
fi

# Display success message and URLs
echo ""
echo -e "${GREEN}🎉 BAC Study App is now running!${NC}"
echo -e "${GREEN}=================================${NC}"
echo ""
echo -e "${CYAN}📱 Frontend (Flutter Web): http://localhost:8080${NC}"
echo -e "${CYAN}🔧 Backend API: http://localhost:3000${NC}"
echo -e "${CYAN}📚 API Documentation: http://localhost:3000/api-docs${NC}"
echo -e "${CYAN}🗄️  Database: localhost:5432 (user: bac_user, db: bac_db)${NC}"
echo ""
echo -e "${YELLOW}📋 Useful Commands:${NC}"
echo -e "${GRAY}   View logs: docker-compose logs -f${NC}"
echo -e "${GRAY}   Stop app: docker-compose down${NC}"
echo -e "${GRAY}   Restart: docker-compose restart${NC}"
echo -e "${GRAY}   Backend shell: docker-compose exec backend bash${NC}"
echo -e "${GRAY}   Frontend shell: docker-compose exec frontend bash${NC}"
echo ""
echo -e "${YELLOW}🔍 To monitor the application:${NC}"
echo -e "${GRAY}   docker-compose logs -f --tail=100${NC}"
echo ""

# Open browser (optional)
read -p "Do you want to open the app in your browser? (Y/n): " open_browser
if [[ ! "$open_browser" =~ ^[Nn]$ ]]; then
    if command -v xdg-open > /dev/null; then
        xdg-open http://localhost:8080 >/dev/null 2>&1 &
        xdg-open http://localhost:3000/api-docs >/dev/null 2>&1 &
    elif command -v open > /dev/null; then
        open http://localhost:8080 >/dev/null 2>&1 &
        open http://localhost:3000/api-docs >/dev/null 2>&1 &
    else
        echo -e "${YELLOW}Please open http://localhost:8080 in your browser${NC}"
    fi
fi

echo -e "${GREEN}Happy coding! 🚀${NC}"
