import 'dart:convert';

class UserModel {
  final String id;
  final String email;
  final String fullName;
  final String? wilaya;
  final int? bacYear;
  final String? bacStream;
  final String? profileImageUrl;
  final bool isEmailVerified;
  final bool hasCompletedOnboarding;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const UserModel({
    required this.id,
    required this.email,
    required this.fullName,
    this.wilaya,
    this.bacYear,
    this.bacStream,
    this.profileImageUrl,
    this.isEmailVerified = false,
    this.hasCompletedOnboarding = false,
    required this.createdAt,
    this.updatedAt,
  });

  // Check if profile is complete
  bool get isProfileComplete {
    return wilaya != null && 
           bacYear != null && 
           bacStream != null &&
           fullName.isNotEmpty;
  }

  // Get display name
  String get displayName {
    return fullName.isNotEmpty ? fullName : email.split('@').first;
  }

  // Get initials for avatar
  String get initials {
    final names = fullName.split(' ');
    if (names.length >= 2) {
      return '${names.first[0]}${names.last[0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names.first[0].toUpperCase();
    }
    return email[0].toUpperCase();
  }

  // Copy with method
  UserModel copyWith({
    String? id,
    String? email,
    String? fullName,
    String? wilaya,
    int? bacYear,
    String? bacStream,
    String? profileImageUrl,
    bool? isEmailVerified,
    bool? hasCompletedOnboarding,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      wilaya: wilaya ?? this.wilaya,
      bacYear: bacYear ?? this.bacYear,
      bacStream: bacStream ?? this.bacStream,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      hasCompletedOnboarding: hasCompletedOnboarding ?? this.hasCompletedOnboarding,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // To JSON
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'fullName': fullName,
      'wilaya': wilaya,
      'bacYear': bacYear,
      'bacStream': bacStream,
      'profileImageUrl': profileImageUrl,
      'isEmailVerified': isEmailVerified,
      'hasCompletedOnboarding': hasCompletedOnboarding,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // From JSON
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      fullName: map['fullName'] ?? '',
      wilaya: map['wilaya'],
      bacYear: map['bacYear']?.toInt(),
      bacStream: map['bacStream'],
      profileImageUrl: map['profileImageUrl'],
      isEmailVerified: map['isEmailVerified'] ?? false,
      hasCompletedOnboarding: map['hasCompletedOnboarding'] ?? false,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory UserModel.fromJson(String source) => UserModel.fromMap(json.decode(source));

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, fullName: $fullName, wilaya: $wilaya, bacYear: $bacYear, bacStream: $bacStream)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is UserModel &&
      other.id == id &&
      other.email == email &&
      other.fullName == fullName &&
      other.wilaya == wilaya &&
      other.bacYear == bacYear &&
      other.bacStream == bacStream &&
      other.profileImageUrl == profileImageUrl &&
      other.isEmailVerified == isEmailVerified &&
      other.hasCompletedOnboarding == hasCompletedOnboarding &&
      other.createdAt == createdAt &&
      other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      email.hashCode ^
      fullName.hashCode ^
      wilaya.hashCode ^
      bacYear.hashCode ^
      bacStream.hashCode ^
      profileImageUrl.hashCode ^
      isEmailVerified.hashCode ^
      hasCompletedOnboarding.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode;
  }
}

// User role enum
enum UserRole {
  student,
  teacher,
  admin,
}

extension UserRoleExtension on UserRole {
  String get name {
    switch (this) {
      case UserRole.student:
        return 'student';
      case UserRole.teacher:
        return 'teacher';
      case UserRole.admin:
        return 'admin';
    }
  }

  String get displayName {
    switch (this) {
      case UserRole.student:
        return 'طالب';
      case UserRole.teacher:
        return 'أستاذ';
      case UserRole.admin:
        return 'مدير';
    }
  }

  static UserRole fromString(String role) {
    switch (role.toLowerCase()) {
      case 'teacher':
        return UserRole.teacher;
      case 'admin':
        return UserRole.admin;
      case 'student':
      default:
        return UserRole.student;
    }
  }
}
