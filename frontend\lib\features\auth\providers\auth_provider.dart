import 'package:flutter_riverpod/flutter_riverpod.dart';

// Placeholder auth state
class AuthState {
  final bool isAuthenticated;
  final String? userId;
  final String? email;

  const AuthState({
    this.isAuthenticated = false,
    this.userId,
    this.email,
  });
}

class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(const AuthState());

  void login(String email) {
    state = AuthState(
      isAuthenticated: true,
      userId: 'user123',
      email: email,
    );
  }

  void logout() {
    state = const AuthState();
  }
}

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});
