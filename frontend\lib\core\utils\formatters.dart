import 'package:intl/intl.dart';

class AppFormatters {
  // Date formatters
  static final DateFormat _arabicDateFormat = DateFormat('dd/MM/yyyy', 'ar');
  static final DateFormat _arabicTimeFormat = DateFormat('HH:mm', 'ar');
  static final DateFormat _arabicDateTimeFormat = DateFormat('dd/MM/yyyy HH:mm', 'ar');
  static final DateFormat _monthYearFormat = DateFormat('MMMM yyyy', 'ar');
  static final DateFormat _dayMonthFormat = DateFormat('dd MMMM', 'ar');

  // Format date to Arabic format
  static String formatDate(DateTime date) {
    return _arabicDateFormat.format(date);
  }

  // Format time to Arabic format
  static String formatTime(DateTime date) {
    return _arabicTimeFormat.format(date);
  }

  // Format date and time to Arabic format
  static String formatDateTime(DateTime date) {
    return _arabicDateTimeFormat.format(date);
  }

  // Format month and year
  static String formatMonthYear(DateTime date) {
    return _monthYearFormat.format(date);
  }

  // Format day and month
  static String formatDayMonth(DateTime date) {
    return _dayMonthFormat.format(date);
  }

  // Format relative time (e.g., "منذ 5 دقائق")
  static String formatRelativeTime(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? 'منذ سنة' : 'منذ $years سنوات';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? 'منذ شهر' : 'منذ $months أشهر';
    } else if (difference.inDays > 0) {
      return difference.inDays == 1 ? 'منذ يوم' : 'منذ ${difference.inDays} أيام';
    } else if (difference.inHours > 0) {
      return difference.inHours == 1 ? 'منذ ساعة' : 'منذ ${difference.inHours} ساعات';
    } else if (difference.inMinutes > 0) {
      return difference.inMinutes == 1 ? 'منذ دقيقة' : 'منذ ${difference.inMinutes} دقائق';
    } else {
      return 'الآن';
    }
  }

  // Format duration (e.g., "1:30:45")
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  // Format file size
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes بايت';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} جيجابايت';
    }
  }

  // Format percentage
  static String formatPercentage(double value, {int decimals = 1}) {
    return '${(value * 100).toStringAsFixed(decimals)}%';
  }

  // Format score
  static String formatScore(double score, double total) {
    return '${score.toStringAsFixed(1)}/${total.toStringAsFixed(1)}';
  }

  // Format grade
  static String formatGrade(double percentage) {
    if (percentage >= 90) {
      return 'ممتاز';
    } else if (percentage >= 80) {
      return 'جيد جداً';
    } else if (percentage >= 70) {
      return 'جيد';
    } else if (percentage >= 60) {
      return 'مقبول';
    } else {
      return 'ضعيف';
    }
  }

  // Format phone number
  static String formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final digits = phoneNumber.replaceAll(RegExp(r'\D'), '');
    
    if (digits.length == 10 && digits.startsWith('0')) {
      // Format: 0555 12 34 56
      return '${digits.substring(0, 4)} ${digits.substring(4, 6)} ${digits.substring(6, 8)} ${digits.substring(8)}';
    } else if (digits.length == 13 && digits.startsWith('213')) {
      // Format: +213 555 12 34 56
      return '+${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6, 8)} ${digits.substring(8, 10)} ${digits.substring(10)}';
    }
    
    return phoneNumber; // Return original if format not recognized
  }

  // Format currency (Algerian Dinar)
  static String formatCurrency(double amount) {
    final formatter = NumberFormat.currency(
      locale: 'ar_DZ',
      symbol: 'دج',
      decimalDigits: 2,
    );
    return formatter.format(amount);
  }

  // Format number with Arabic digits
  static String formatArabicNumber(int number) {
    const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final index = int.tryParse(digit);
      return index != null ? arabicDigits[index] : digit;
    }).join();
  }

  // Format text to title case
  static String toTitleCase(String text) {
    return text.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  // Truncate text with ellipsis
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  // Format quiz time remaining
  static String formatTimeRemaining(Duration remaining) {
    final minutes = remaining.inMinutes;
    final seconds = remaining.inSeconds.remainder(60);
    
    if (minutes > 0) {
      return '$minutes:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '0:${seconds.toString().padLeft(2, '0')}';
    }
  }

  // Format study streak
  static String formatStudyStreak(int days) {
    if (days == 0) {
      return 'لا توجد سلسلة دراسة';
    } else if (days == 1) {
      return 'يوم واحد';
    } else if (days <= 10) {
      return '$days أيام';
    } else {
      return '$days يوماً';
    }
  }

  // Format BAC stream in Arabic
  static String formatBacStream(String stream) {
    switch (stream.toLowerCase()) {
      case 'sciences':
      case 'علوم تجريبية':
        return 'علوم تجريبية';
      case 'mathematics':
      case 'رياضيات':
        return 'رياضيات';
      case 'technique':
      case 'تقني رياضي':
        return 'تقني رياضي';
      case 'gestion':
      case 'تسيير واقتصاد':
        return 'تسيير واقتصاد';
      case 'lettres':
      case 'آداب وفلسفة':
        return 'آداب وفلسفة';
      case 'langues':
      case 'لغات أجنبية':
        return 'لغات أجنبية';
      default:
        return stream;
    }
  }

  // Format content type in Arabic
  static String formatContentType(String type) {
    switch (type.toLowerCase()) {
      case 'pdf':
        return 'ملف PDF';
      case 'video':
        return 'فيديو';
      case 'image':
        return 'صورة';
      case 'text':
        return 'نص';
      case 'audio':
        return 'صوت';
      default:
        return type;
    }
  }

  // Format difficulty level
  static String formatDifficultyLevel(int level) {
    switch (level) {
      case 1:
        return 'سهل';
      case 2:
        return 'متوسط';
      case 3:
        return 'صعب';
      case 4:
        return 'صعب جداً';
      case 5:
        return 'خبير';
      default:
        return 'غير محدد';
    }
  }
}
