const { Pool } = require('pg');
const config = require('../config/config');
const logger = require('../utils/logger');

// Create PostgreSQL connection pool
const pool = new Pool({
  connectionString: config.database.url,
  ssl: config.database.ssl,
  min: config.database.pool.min,
  max: config.database.pool.max,
  acquireTimeoutMillis: config.database.pool.acquire,
  idleTimeoutMillis: config.database.pool.idle,
});

// Pool event handlers
pool.on('connect', (client) => {
  logger.info('New database client connected');
});

pool.on('error', (err, client) => {
  logger.error('Unexpected error on idle database client:', err);
});

pool.on('acquire', (client) => {
  logger.debug('Database client acquired from pool');
});

pool.on('release', (client) => {
  logger.debug('Database client released back to pool');
});

// Database connection function
async function connectDB() {
  try {
    // Test the connection
    const client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    client.release();
    
    logger.info('Database connected successfully at:', result.rows[0].now);
    return pool;
  } catch (error) {
    logger.error('Database connection failed:', error);
    throw error;
  }
}

// Query helper function with error handling
async function query(text, params = []) {
  const start = Date.now();
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    
    logger.debug('Executed query', {
      text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      duration: `${duration}ms`,
      rows: result.rowCount
    });
    
    return result;
  } catch (error) {
    logger.error('Query error:', {
      text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      error: error.message,
      params: params
    });
    throw error;
  }
}

// Transaction helper function
async function transaction(callback) {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Transaction rolled back:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Get client for manual transaction management
async function getClient() {
  return await pool.connect();
}

// Close all connections
async function closeDB() {
  try {
    await pool.end();
    logger.info('Database connections closed');
  } catch (error) {
    logger.error('Error closing database connections:', error);
    throw error;
  }
}

// Health check function
async function healthCheck() {
  try {
    const result = await query('SELECT 1 as health_check');
    return result.rows[0].health_check === 1;
  } catch (error) {
    logger.error('Database health check failed:', error);
    return false;
  }
}

// Database statistics
async function getStats() {
  try {
    const stats = await query(`
      SELECT 
        schemaname,
        tablename,
        attname,
        n_distinct,
        correlation
      FROM pg_stats 
      WHERE schemaname = 'public'
      LIMIT 10
    `);
    
    const connections = await query(`
      SELECT 
        count(*) as total_connections,
        count(*) FILTER (WHERE state = 'active') as active_connections,
        count(*) FILTER (WHERE state = 'idle') as idle_connections
      FROM pg_stat_activity 
      WHERE datname = current_database()
    `);
    
    return {
      tableStats: stats.rows,
      connectionStats: connections.rows[0],
      poolStats: {
        totalCount: pool.totalCount,
        idleCount: pool.idleCount,
        waitingCount: pool.waitingCount
      }
    };
  } catch (error) {
    logger.error('Error getting database stats:', error);
    return null;
  }
}

module.exports = {
  pool,
  connectDB,
  query,
  transaction,
  getClient,
  closeDB,
  healthCheck,
  getStats
};
