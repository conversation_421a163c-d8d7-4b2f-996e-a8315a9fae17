class AppLogger {
  static void init() {
    // Initialize logger
  }

  static void info(String message) {
    print('[INFO] $message');
  }

  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    print('[ERROR] $message');
    if (error != null) print('Error: $error');
    if (stackTrace != null) print('Stack trace: $stackTrace');
  }

  static void debug(String message) {
    print('[DEBUG] $message');
  }
}
