const jwt = require('jsonwebtoken');
const admin = require('firebase-admin');
const config = require('../config/config');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/error.middleware');

class AuthService {
  constructor() {
    this.initializeFirebase();
  }

  /**
   * Initialize Firebase Admin SDK
   */
  initializeFirebase() {
    if (!admin.apps.length && config.firebase.projectId) {
      try {
        admin.initializeApp({
          credential: admin.credential.cert({
            projectId: config.firebase.projectId,
            privateKey: config.firebase.privateKey,
            clientEmail: config.firebase.clientEmail,
          }),
          databaseURL: config.firebase.databaseURL,
        });
        logger.info('Firebase Admin SDK initialized');
      } catch (error) {
        logger.warn('Firebase Admin SDK initialization failed:', error.message);
      }
    }
  }

  /**
   * Verify Firebase ID token
   */
  async verifyFirebaseToken(token) {
    try {
      if (!admin.apps.length) {
        throw new AppError('Firebase not initialized', 500);
      }

      const decodedToken = await admin.auth().verifyIdToken(token);
      return decodedToken;
    } catch (error) {
      logger.error('Firebase token verification failed:', error);
      throw new AppError('Invalid Firebase token', 401);
    }
  }

  /**
   * Generate JWT access token
   */
  async generateAccessToken(user) {
    const payload = {
      userId: user.id,
      firebaseUid: user.firebase_uid,
      email: user.email,
      role: user.role || 'student',
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
      issuer: 'bac-study-app',
      audience: 'bac-study-app-users',
    });
  }

  /**
   * Generate JWT refresh token
   */
  async generateRefreshToken(user) {
    const payload = {
      userId: user.id,
      firebaseUid: user.firebase_uid,
      type: 'refresh',
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: 'bac-study-app',
      audience: 'bac-study-app-users',
    });
  }

  /**
   * Generate both access and refresh tokens
   */
  async generateTokens(user) {
    const [accessToken, refreshToken] = await Promise.all([
      this.generateAccessToken(user),
      this.generateRefreshToken(user),
    ]);

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      token_type: 'Bearer',
      expires_in: this.getTokenExpirationTime(config.jwt.expiresIn),
    };
  }

  /**
   * Verify JWT token
   */
  async verifyToken(token) {
    try {
      const decoded = jwt.verify(token, config.jwt.secret, {
        issuer: 'bac-study-app',
        audience: 'bac-study-app-users',
      });
      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new AppError('Token expired', 401);
      } else if (error.name === 'JsonWebTokenError') {
        throw new AppError('Invalid token', 401);
      } else {
        throw new AppError('Token verification failed', 401);
      }
    }
  }

  /**
   * Verify refresh token
   */
  async verifyRefreshToken(token) {
    try {
      const decoded = await this.verifyToken(token);
      
      if (decoded.type !== 'refresh') {
        throw new AppError('Invalid refresh token', 401);
      }

      return decoded;
    } catch (error) {
      throw new AppError('Invalid refresh token', 401);
    }
  }

  /**
   * Get token expiration time in seconds
   */
  getTokenExpirationTime(expiresIn) {
    const timeUnits = {
      s: 1,
      m: 60,
      h: 3600,
      d: 86400,
    };

    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) {
      return 3600; // Default to 1 hour
    }

    const [, value, unit] = match;
    return parseInt(value) * timeUnits[unit];
  }

  /**
   * Invalidate user tokens (for logout)
   */
  async invalidateUserTokens(userId) {
    // In a production app, you might want to maintain a blacklist of tokens
    // or store refresh tokens in the database and remove them here
    logger.info(`Invalidating tokens for user: ${userId}`);
    
    // For now, we'll just log the action
    // In the future, you could:
    // 1. Add tokens to a blacklist in Redis
    // 2. Remove refresh tokens from database
    // 3. Update user's token version in database
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(user) {
    try {
      // Generate password reset token
      const resetToken = jwt.sign(
        { userId: user.id, type: 'password_reset' },
        config.jwt.secret,
        { expiresIn: '1h' }
      );

      // In a real application, you would send an email here
      // For now, we'll just log the token
      logger.info(`Password reset token for ${user.email}: ${resetToken}`);

      // TODO: Implement email sending
      // await emailService.sendPasswordResetEmail(user.email, resetToken);

      return { success: true, token: resetToken };
    } catch (error) {
      logger.error('Send password reset email error:', error);
      throw new AppError('Failed to send password reset email', 500);
    }
  }

  /**
   * Verify password reset token
   */
  async verifyPasswordResetToken(token) {
    try {
      const decoded = jwt.verify(token, config.jwt.secret);
      
      if (decoded.type !== 'password_reset') {
        throw new AppError('Invalid password reset token', 400);
      }

      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new AppError('Password reset token expired', 400);
      }
      throw new AppError('Invalid password reset token', 400);
    }
  }

  /**
   * Send email verification
   */
  async sendEmailVerification(user) {
    try {
      // Generate email verification token
      const verificationToken = jwt.sign(
        { userId: user.id, type: 'email_verification' },
        config.jwt.secret,
        { expiresIn: '24h' }
      );

      // In a real application, you would send an email here
      logger.info(`Email verification token for ${user.email}: ${verificationToken}`);

      // TODO: Implement email sending
      // await emailService.sendEmailVerification(user.email, verificationToken);

      return { success: true, token: verificationToken };
    } catch (error) {
      logger.error('Send email verification error:', error);
      throw new AppError('Failed to send email verification', 500);
    }
  }

  /**
   * Verify email verification token
   */
  async verifyEmailToken(token) {
    try {
      const decoded = jwt.verify(token, config.jwt.secret);
      
      if (decoded.type !== 'email_verification') {
        throw new AppError('Invalid email verification token', 400);
      }

      // TODO: Update user's email_verified status in database
      // await userService.markEmailAsVerified(decoded.userId);

      return { success: true, userId: decoded.userId };
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new AppError('Email verification token expired', 400);
      }
      throw new AppError('Invalid email verification token', 400);
    }
  }

  /**
   * Generate API key for external integrations
   */
  async generateApiKey(user, purpose = 'general') {
    const payload = {
      userId: user.id,
      type: 'api_key',
      purpose,
      createdAt: new Date().toISOString(),
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: '1y', // API keys last longer
      issuer: 'bac-study-app',
      audience: 'bac-study-app-api',
    });
  }

  /**
   * Verify API key
   */
  async verifyApiKey(apiKey) {
    try {
      const decoded = jwt.verify(apiKey, config.jwt.secret, {
        issuer: 'bac-study-app',
        audience: 'bac-study-app-api',
      });

      if (decoded.type !== 'api_key') {
        throw new AppError('Invalid API key', 401);
      }

      return decoded;
    } catch (error) {
      throw new AppError('Invalid API key', 401);
    }
  }

  /**
   * Check if user has permission
   */
  hasPermission(user, permission) {
    // Simple role-based permission system
    const rolePermissions = {
      student: ['read_content', 'take_quiz', 'post_forum'],
      teacher: ['read_content', 'create_content', 'moderate_forum', 'view_analytics'],
      admin: ['*'], // Admin has all permissions
    };

    const userRole = user.role || 'student';
    const permissions = rolePermissions[userRole] || [];

    return permissions.includes('*') || permissions.includes(permission);
  }

  /**
   * Generate temporary access token for file downloads
   */
  async generateFileAccessToken(user, fileId, expiresIn = '1h') {
    const payload = {
      userId: user.id,
      fileId,
      type: 'file_access',
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn,
      issuer: 'bac-study-app',
      audience: 'bac-study-app-files',
    });
  }

  /**
   * Verify file access token
   */
  async verifyFileAccessToken(token) {
    try {
      const decoded = jwt.verify(token, config.jwt.secret, {
        issuer: 'bac-study-app',
        audience: 'bac-study-app-files',
      });

      if (decoded.type !== 'file_access') {
        throw new AppError('Invalid file access token', 401);
      }

      return decoded;
    } catch (error) {
      throw new AppError('Invalid file access token', 401);
    }
  }
}

module.exports = new AuthService();
