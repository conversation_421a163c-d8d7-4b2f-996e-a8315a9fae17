const express = require('express');
const path = require('path');

const app = express();
const PORT = 8080;

// Serve static files from frontend/web directory
app.use(express.static(path.join(__dirname, 'frontend', 'web')));

// Handle all routes by serving index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend', 'web', 'index.html'));
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🎨 Simple Frontend Server running on port ${PORT}`);
  console.log(`📱 Open: http://localhost:${PORT}`);
});

module.exports = app;
